import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { AnalyticsDashboard } from '@/components/analytics/analytics-dashboard'

export default async function AnalyticsPage() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/login')
  }
  
  const { data: projects } = await supabase
    .from('projects')
    .select('id, title, primary_genre')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
  
  return (
    <div className="min-h-screen bg-background">
      <div className="fixed inset-0 paper-texture opacity-30" />
      
      <div className="relative z-10 py-8">
        <div className="container">
          <div className="mb-8">
            <h1 className="text-3xl font-literary-display text-foreground mb-2">
              Writing Analytics
            </h1>
            <p className="text-muted-foreground">
              Track your writing progress, analyze your productivity, and improve your craft
            </p>
          </div>
          
          <AnalyticsDashboard 
            userId={user.id} 
            projects={projects || []}
          />
        </div>
      </div>
    </div>
  )
}