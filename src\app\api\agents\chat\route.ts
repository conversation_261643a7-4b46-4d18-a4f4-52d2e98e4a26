import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { openai } from '@/lib/openai'
import { getClientIP } from '@/lib/rate-limiter'
import { AI_RATE_LIMITS, createAIRateLimitResponse } from '@/lib/rate-limiter-ai'
import { authenticateUser, validateProjectOwnership, handleRouteError } from '@/lib/auth'
import type { Project } from '@/lib/db/types'
import { z } from 'zod'
import { AI_MODELS } from '@/lib/config/ai-settings'

interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

// Validation schemas
const chatMessageSchema = z.object({
  role: z.enum(['user', 'assistant', 'system']),
  content: z.string().max(10000, 'Message content too long')
});

const chatRequestSchema = z.object({
  projectId: z.string().uuid('Invalid project ID format'),
  chapterId: z.string().uuid('Invalid chapter ID format').optional(),
  message: z.string()
    .min(1, 'Message cannot be empty')
    .max(4000, 'Message must not exceed 4000 characters'),
  context: z.object({
    selectedText: z.string().max(5000).optional(),
    chatHistory: z.array(chatMessageSchema).max(50).optional()
  }).optional()
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting check
    const clientIP = getClientIP(request)
    const { limiter, requests } = AI_RATE_LIMITS.chat
    const rateLimitResult = limiter.check(requests, clientIP)
    
    if (!rateLimitResult.success) {
      return createAIRateLimitResponse(rateLimitResult.reset)
    }
    
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }
    const { user, supabase } = authResult
    
    if (!user || !supabase) {
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate request body
    const validationResult = chatRequestSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid request data', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      )
    }
    
    const { projectId, chapterId, message, context } = validationResult.data

    // Validate project ownership
    const ownershipResult = await validateProjectOwnership(supabase, user.id, projectId)
    if (!ownershipResult.success) {
      return ownershipResult.response!
    }
    const project = ownershipResult.data as Project

    // Get current chapter context if available
    let chapterContext = ''
    if (chapterId) {
      const { data: chapter } = await supabase
        .from('chapters')
        .select('*')
        .eq('id', chapterId)
        .single()
      
      if (chapter) {
        chapterContext = `Current Chapter: ${chapter.chapter_number} - "${chapter.title}"\n`
        if (chapter.content) {
          chapterContext += `Current content: ${chapter.content.slice(-1000)}...\n`
        }
      }
    }

    // Build context-aware system prompt
    const systemPrompt = `You are an expert AI writing assistant for BookScribe AI, helping authors write novels. You provide helpful, specific advice about writing, editing, character development, plot structure, and creative techniques.

PROJECT CONTEXT:
- Title: ${project.title || 'Untitled Project'}
- Genre: ${project.primary_genre || 'Not specified'}${project.subgenre ? ` (${project.subgenre})` : ''}
- Writing Style: ${project.writing_style || 'Not specified'}
- Narrative Voice: ${project.narrative_voice || 'Not specified'}
- Target Audience: ${project.target_audience || 'Not specified'}
- Target Word Count: ${project.target_word_count?.toLocaleString() || 'Not specified'} words

${chapterContext}

${context?.selectedText ? `SELECTED TEXT: "${context.selectedText}"\n` : ''}

GUIDELINES:
- Provide specific, actionable advice
- Stay in character as a writing mentor
- Reference the project context when relevant
- If the user selected text, focus on that specific content
- Offer concrete suggestions rather than generic advice
- Help with character consistency, plot development, and writing quality
- Be encouraging and constructive

Remember: You're helping create a ${project.primary_genre || 'genre'} novel with ${project.narrative_voice || 'narrative'} perspective.`

    // Build conversation history
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt }
    ]

    // Add recent chat history for context
    if (context?.chatHistory) {
      context.chatHistory.forEach((msg: ChatMessage) => {
        messages.push({
          role: msg.role,
          content: msg.content
        })
      })
    }

    // Add current message
    messages.push({ role: 'user', content: message })

    const response = await openai.chat.completions.create({
      model: AI_MODELS.PRIMARY,
      temperature: 0.8,
      max_tokens: 1500,
      messages: messages
    })

    const aiResponse = response.choices[0]?.message?.content?.trim()

    if (!aiResponse) {
      return NextResponse.json({ error: 'No response from AI' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      response: aiResponse,
      tokensUsed: response.usage?.total_tokens
    })

  } catch (error) {
    return handleRouteError(error, 'AI Chat')
  }
}