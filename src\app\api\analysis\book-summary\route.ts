import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { authenticateUser, handleRouteError } from '@/lib/auth'
import { openai } from '@/lib/openai'
import { AI_MODELS } from '@/lib/config/ai-settings'
import { withRetry, ErrorContext } from '@/lib/services/error-handler'
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from '@/lib/services/circuit-breaker'

interface SummaryRequest {
  projectId: string
  summaryType: string
  targetAudience: string
  tone: string
  regenerate?: boolean
}

interface Character {
  name: string
  role: string
  description: string
}

interface ChapterSummary {
  title: string
  summary: string
}

const summaryPrompts = {
  'elevator-pitch': `Create a compelling 30-second elevator pitch (approximately 50 words) that captures the essence of this book. Focus on the hook and what makes it unique.`,
  
  'back-cover': `Write an engaging back cover blurb (approximately 150 words) that will entice readers to buy the book. Include a hook, main conflict, and leave them wanting more without spoilers.`,
  
  'synopsis': `Write a detailed synopsis (approximately 500 words) suitable for literary agents and publishers. Include all major plot points, character arcs, and the ending.`,
  
  'query-letter': `Write the book description portion of a query letter (approximately 300 words) that would appeal to literary agents. Focus on conflict, stakes, and what makes this book marketable.`,
  
  'amazon-description': `Write an SEO-optimized book description (approximately 200 words) for online retailers like Amazon. Use engaging language, break up text for readability, and highlight key selling points.`
}

export async function POST(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateUser()
    if (!authResult.success) {
      return authResult.response!
    }

    const body: SummaryRequest = await request.json()
    const { projectId, summaryType, targetAudience, tone, regenerate } = body

    if (!projectId || !summaryType) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Check if OpenAI is configured
    if (!openai.apiKey || openai.apiKey === 'sk-dummy-key-for-development') {
      return NextResponse.json({ 
        error: 'OpenAI API key not configured. Please add your API key to use AI features.' 
      }, { status: 503 })
    }

    const supabase = await createClient()

    // Get project and chapters
    const { data: project } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single()

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Get all chapters
    const { data: chapters } = await supabase
      .from('chapters')
      .select('chapter_number, title, content, summary')
      .eq('project_id', projectId)
      .order('chapter_number')

    if (!chapters || chapters.length === 0) {
      return NextResponse.json({ error: 'No chapters found' }, { status: 400 })
    }

    // Get story bible
    const { data: storyBible } = await supabase
      .from('story_bible')
      .select('*')
      .eq('project_id', projectId)
      .single()

    // Prepare context for AI
    const bookContext = {
      title: project.title,
      genre: project.genre,
      subgenre: project.subgenre,
      description: project.description,
      targetAudience: project.target_audience || targetAudience,
      themes: storyBible?.themes || [],
      totalWordCount: project.word_count,
      chapterCount: chapters.length,
      chapterSummaries: chapters.map((ch: {chapter_number: number; title: string; summary?: string}) => ({
        number: ch.chapter_number,
        title: ch.title,
        summary: ch.summary || `Chapter ${ch.chapter_number}: ${ch.title}`
      }))
    }

    // Get the main characters
    const mainCharacters: Character[] = storyBible?.characters?.slice(0, 3).map((char: Character) => ({
      name: char.name,
      role: char.role,
      description: char.description
    })) || []

    // Build the prompt
    const systemPrompt = `You are a professional book marketing expert and literary agent. Generate compelling book summaries that sell books.`
    
    const userPrompt = `
Generate a ${summaryType} for this book:

Title: ${bookContext.title}
Genre: ${bookContext.genre} - ${bookContext.subgenre}
Target Audience: ${targetAudience}
Tone: ${tone}
Word Count: ${bookContext.totalWordCount?.toLocaleString()} words
Chapters: ${bookContext.chapterCount}

Book Description: ${bookContext.description}

Main Characters:
${mainCharacters.map((char: Character) => `- ${char.name} (${char.role}): ${char.description}`).join('\n')}

Themes: ${bookContext.themes.join(', ')}

Chapter Overview:
${bookContext.chapterSummaries.slice(0, 5).map((ch: ChapterSummary) => `- ${ch.title}: ${ch.summary}`).join('\n')}
${bookContext.chapterSummaries.length > 5 ? `...and ${bookContext.chapterSummaries.length - 5} more chapters` : ''}

Instructions: ${summaryPrompts[summaryType as keyof typeof summaryPrompts]}

Make the summary ${tone} in tone and appealing to ${targetAudience}.`

    // Generate summary
    const errorContext: ErrorContext = {
      operation: 'BookSummaryGeneration',
      projectId,
      metadata: {
        summaryType,
        model: AI_MODELS.PRIMARY
      }
    }

    const completion = await withRetry(
      async () => withCircuitBreaker(
        'openai-book-summary',
        () => openai.chat.completions.create({
          model: AI_MODELS.PRIMARY,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: tone === 'dramatic' ? 0.9 : tone === 'casual' ? 0.8 : 0.7,
          max_tokens: 1000
        }),
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
      {
        maxRetries: 2,
        onRetry: (error, attempt) => {
          console.log(`Book summary generation retry ${attempt}:`, error.message)
        }
      },
      errorContext
    )

    const summary = completion.choices[0]?.message.content || ''

    // Save to database - try book_summaries table first, fall back to story_bible
    try {
      const summaryRecord = {
        project_id: projectId,
        type: summaryType,
        content: summary,
        metadata: {
          targetAudience,
          tone,
          generatedAt: new Date().toISOString()
        }
      }

      if (regenerate) {
        const { error } = await supabase
          .from('book_summaries')
          .update(summaryRecord)
          .eq('project_id', projectId)
          .eq('type', summaryType)
        
        if (error) throw error
      } else {
        const { error } = await supabase
          .from('book_summaries')
          .insert(summaryRecord)
        
        if (error) throw error
      }
    } catch (dbError) {
      // If book_summaries table doesn't exist, update story_bible instead
      console.warn('book_summaries table not found, updating story_bible instead:', dbError)
      
      const { data: currentBible } = await supabase
        .from('story_bible')
        .select('marketing_materials')
        .eq('project_id', projectId)
        .single()
      
      const marketingMaterials = currentBible?.marketing_materials || {}
      marketingMaterials[summaryType] = {
        content: summary,
        targetAudience,
        tone,
        generatedAt: new Date().toISOString()
      }
      
      await supabase
        .from('story_bible')
        .update({ marketing_materials: marketingMaterials })
        .eq('project_id', projectId)
    }

    return NextResponse.json({
      success: true,
      summary,
      wordCount: summary.split(/\s+/).filter(w => w.length > 0).length
    })

  } catch (error) {
    return handleRouteError(error, 'Book Summary Generation')
  }
}