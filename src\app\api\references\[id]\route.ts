import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: materialId } = await params;

    const { data: material, error } = await supabase
      .from('reference_materials')
      .select('*')
      .eq('id', materialId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Material not found' }, { status: 404 });
      }
      throw error;
    }

    // Transform response to match frontend interface
    const formattedMaterial = {
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    };

    return NextResponse.json({ material: formattedMaterial });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: materialId } = await params;
    const body = await request.json();
    const { title, description, tags, content } = body;

    const updates: Record<string, string | string[]> = {};
    
    if (title !== undefined) updates.title = title;
    if (description !== undefined) updates.description = description;
    if (tags !== undefined) updates.tags = tags;
    if (content !== undefined) updates.content = content;
    
    updates.updated_at = new Date().toISOString();

    const { data: material, error } = await supabase
      .from('reference_materials')
      .update(updates)
      .eq('id', materialId)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Material not found' }, { status: 404 });
      }
      throw error;
    }

    // Transform response to match frontend interface
    const formattedMaterial = {
      id: material.id,
      projectId: material.project_id,
      type: material.type,
      title: material.title,
      description: material.description,
      fileUrl: material.file_url,
      fileSize: material.file_size,
      mimeType: material.mime_type,
      content: material.content,
      tags: material.tags || [],
      aiSummary: material.ai_summary,
      createdAt: new Date(material.created_at),
      updatedAt: new Date(material.updated_at),
    };

    return NextResponse.json({ material: formattedMaterial });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: materialId } = await params;

    // First get the material to check if it has a file to delete
    const { data: material, error: fetchError } = await supabase
      .from('reference_materials')
      .select('file_url')
      .eq('id', materialId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Material not found' }, { status: 404 });
      }
      throw fetchError;
    }

    // Delete the file from storage if it exists
    if (material.file_url) {
      try {
        // Extract file path from URL
        const url = new URL(material.file_url);
        const filePath = url.pathname.split('/storage/v1/object/public/project-files/')[1];
        
        if (filePath) {
          await supabase.storage
            .from('project-files')
            .remove([filePath]);
        }
      } catch (storageError) {
        console.warn('Failed to delete file from storage:', storageError);
        // Continue with database deletion even if file deletion fails
      }
    }

    // Delete the database record
    const { error: deleteError } = await supabase
      .from('reference_materials')
      .delete()
      .eq('id', materialId);

    if (deleteError) throw deleteError;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}