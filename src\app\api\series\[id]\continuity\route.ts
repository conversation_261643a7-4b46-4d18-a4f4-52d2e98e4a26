import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server';
import { supabase } from '@/lib/db/client';
import OpenAI from 'openai';
import type { Character, StoryBible } from '@/lib/db/types';
import { config } from '@/lib/config';

// Series continuity analysis types
interface SeriesBook {
  id: string;
  name: string;
  description?: string;
  status: string;
  series_order: number;
  characters?: Character[];
  story_bible?: StoryBible[];
}

interface TimelineEventData {
  date: string;
  title?: string;
  event?: string;
  order?: number;
}

interface WorldBuildingData {
  title?: string;
  content?: string;
  category?: string;
}

interface ContinuityIssue {
  type: string;
  severity: 'low' | 'medium' | 'high';
  bookIndex?: number;
  character?: string;
  message: string;
  books?: number[];
  events?: TimelineEvent[];
}

interface ContinuitySuggestion {
  type: string;
  message: string;
  priority?: 'low' | 'medium' | 'high';
}

interface TimelineEvent {
  date: string;
  event: string;
  book: number;
  order?: number;
}

interface ContinuityAnalysis {
  continuityScore: number;
  issues: ContinuityIssue[];
  suggestions: ContinuitySuggestion[];
  characterCount?: number;
  timelineEvents?: number;
  booksAnalyzed: number;
  worldElements?: number;
  relationships?: number;
  analysisType?: string;
  aiAnalysis?: string;
  aiSuggestions?: ContinuitySuggestion[];
}

interface SeriesWithBooks {
  id: string;
  name: string;
  description?: string;
  genre?: string;
  books?: SeriesBook[];
}

const openai = new OpenAI({
  apiKey: config.openai.apiKey,
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: seriesId } = await params;
    const { searchParams } = new URL(request.url);
    const checkType = searchParams.get('type') || 'basic'; // basic, detailed, ai-analysis

    // Get series with all books
    const { data: series, error: seriesError } = await supabase
      .from('book_series')
      .select(`
        *,
        books:projects!series_id(
          id,
          name,
          description,
          status,
          series_order,
          characters(
            id,
            name,
            role,
            description,
            personality_traits,
            relationships
          ),
          story_bible(
            id,
            category,
            title,
            content,
            timeline_date,
            timeline_order
          )
        )
      `)
      .eq('id', seriesId)
      .single();

    if (seriesError) {
      if (seriesError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Series not found' }, { status: 404 });
      }
      throw seriesError;
    }

    const books = (series.books || []).sort((a: SeriesBook, b: SeriesBook) => a.series_order - b.series_order);

    if (books.length < 2) {
      return NextResponse.json({
        continuityScore: 100,
        issues: [],
        suggestions: [],
        message: 'Series needs at least 2 books for continuity analysis'
      });
    }

    // Perform continuity checks based on type
    let continuityAnalysis;
    
    switch (checkType) {
      case 'detailed':
        continuityAnalysis = await performDetailedContinuityCheck(books);
        break;
      case 'ai-analysis':
        continuityAnalysis = await performAIContinuityAnalysis(books, series);
        break;
      default:
        continuityAnalysis = await performBasicContinuityCheck(books);
    }

    return NextResponse.json(continuityAnalysis);
  } catch (error) {
    console.error('Continuity check API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function performBasicContinuityCheck(books: SeriesBook[]): Promise<ContinuityAnalysis> {
  const issues: ContinuityIssue[] = [];
  const suggestions: ContinuitySuggestion[] = [];
  let totalScore = 100;

  // Check for character consistency across books
  const allCharacters = new Map();
  
  books.forEach((book, index) => {
    const characters = book.characters || [];
    
    characters.forEach((char: Character) => {
      if (allCharacters.has(char.name)) {
        const existingChar = allCharacters.get(char.name);
        
        // Check for role changes
        if (existingChar.role !== char.role) {
          issues.push({
            type: 'character_role_inconsistency',
            severity: 'medium',
            bookIndex: index,
            character: char.name,
            message: `${char.name} has different roles across books: ${existingChar.role} vs ${char.role}`,
            books: [existingChar.bookIndex, index]
          });
          totalScore -= 10;
        }
      } else {
        allCharacters.set(char.name, { ...char, bookIndex: index });
      }
    });
  });

  // Check for timeline consistency
  const timelineEvents: TimelineEvent[] = [];
  books.forEach((book, index) => {
    const storyBible = book.story_bible || [];
    storyBible.forEach((entry: StoryBible) => {
      if (entry.entry_type === 'timeline_event' && entry.entry_data) {
        const data = entry.entry_data as unknown as TimelineEventData;
        if (data.date) {
          timelineEvents.push({
            date: data.date,
            event: data.title || data.event || entry.entry_key,
            book: index,
            order: data.order || undefined
          });
        }
      }
    });
  });

  // Sort timeline events and check for inconsistencies
  timelineEvents.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  for (let i = 0; i < timelineEvents.length - 1; i++) {
    const current = timelineEvents[i];
    const next = timelineEvents[i + 1];
    
    if (current && next && current.book > next.book) {
      issues.push({
        type: 'timeline_inconsistency',
        severity: 'high',
        message: `Timeline event "${current.event}" in book ${current.book + 1} occurs before "${next.event}" in book ${next.book + 1}`,
        events: [current, next]
      });
      totalScore -= 15;
    }
  }

  // Generate suggestions
  if (issues.length === 0) {
    suggestions.push({
      type: 'maintain_quality',
      message: 'Excellent continuity! Continue tracking character development and timeline events.'
    });
  } else {
    if (issues.some(i => i.type === 'character_role_inconsistency')) {
      suggestions.push({
        type: 'character_consistency',
        message: 'Create a character development tracker to maintain consistent character roles and growth across books.'
      });
    }
    
    if (issues.some(i => i.type === 'timeline_inconsistency')) {
      suggestions.push({
        type: 'timeline_management',
        message: 'Use the timeline validation tools to ensure chronological consistency across your series.'
      });
    }
  }

  return {
    continuityScore: Math.max(0, totalScore),
    issues,
    suggestions,
    characterCount: allCharacters.size,
    timelineEvents: timelineEvents.length,
    booksAnalyzed: books.length
  };
}

async function performDetailedContinuityCheck(books: SeriesBook[]): Promise<ContinuityAnalysis> {
  const basicCheck = await performBasicContinuityCheck(books);
  
  // Additional detailed checks
  const additionalIssues: ContinuityIssue[] = [];
  let detailedScore = basicCheck.continuityScore;

  // Check world-building consistency
  const worldElements = new Map();
  
  books.forEach((book, bookIndex) => {
    const storyBible = book.story_bible || [];
    
    storyBible.forEach((entry: StoryBible) => {
      const data = entry.entry_data as WorldBuildingData;
      if (entry.entry_type === 'world_building' || entry.entry_type === 'setting' || 
          (data && (data.category === 'world-building' || data.category === 'setting'))) {
        const title = data.title || entry.entry_key;
        const key = title.toLowerCase();
        
        if (worldElements.has(key)) {
          const existing = worldElements.get(key);
          if (existing.content !== data.content) {
            additionalIssues.push({
              type: 'world_building_inconsistency',
              severity: 'medium',
              message: `World element "${title}" has conflicting descriptions across books`,
              books: [existing.bookIndex, bookIndex]
            });
            detailedScore -= 8;
          }
        } else {
          worldElements.set(key, { ...data, bookIndex });
        }
      }
    });
  });

  // Check character relationship consistency
  const relationships = new Map();
  
  books.forEach((book, bookIndex) => {
    const characters = book.characters || [];
    
    characters.forEach((char: Character) => {
      if (char.relationships && typeof char.relationships === 'object') {
        Object.entries(char.relationships).forEach(([otherChar, relationship]: [string, unknown]) => {
          const relationshipKey = [char.name, otherChar].sort().join('-');
          
          if (relationships.has(relationshipKey)) {
            // This would need more sophisticated comparison logic
            // For now, just track that relationships exist
          } else {
            relationships.set(relationshipKey, { relationship, bookIndex });
          }
        });
      }
    });
  });

  return {
    ...basicCheck,
    continuityScore: Math.max(0, detailedScore),
    issues: [...basicCheck.issues, ...additionalIssues],
    worldElements: worldElements.size,
    relationships: relationships.size,
    analysisType: 'detailed'
  };
}

async function performAIContinuityAnalysis(books: SeriesBook[], series: SeriesWithBooks): Promise<ContinuityAnalysis> {
  try {
    const basicCheck = await performDetailedContinuityCheck(books);
    
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: `You are an expert literary editor specializing in series continuity analysis. Analyze the provided book series data and identify potential continuity issues, plot holes, and areas for improvement.

Focus on:
1. Character development arcs across books
2. Plot thread consistency
3. World-building continuity
4. Thematic coherence
5. Pacing and structure flow

Provide specific, actionable feedback that helps maintain series continuity.`
        },
        {
          role: 'user',
          content: `Please analyze this book series for continuity:

Series: ${series.name}
Genre: ${series.genre}
Description: ${series.description}

Books in order:
${books.map(book => `${book.series_order}. ${book.name} - ${book.description}`).join('\n')}

Basic continuity check found ${basicCheck.issues.length} issues with a score of ${basicCheck.continuityScore}/100.

Provide additional insights and recommendations for maintaining excellent series continuity.`
        }
      ],
      max_tokens: 800,
      temperature: 0.7,
    });

    const aiAnalysis = completion.choices[0]?.message?.content || '';

    return {
      ...basicCheck,
      aiAnalysis,
      aiSuggestions: extractAISuggestions(aiAnalysis),
      analysisType: 'ai-enhanced'
    };
  } catch (error) {
    console.error('AI continuity analysis failed:', error);
    // Fall back to detailed analysis if AI fails
    return await performDetailedContinuityCheck(books);
  }
}

function extractAISuggestions(aiAnalysis: string): ContinuitySuggestion[] {
  // Simple extraction logic - in production, this could be more sophisticated
  const suggestions: ContinuitySuggestion[] = [];
  const lines = aiAnalysis.split('\n');
  
  lines.forEach(line => {
    if (line.includes('suggest') || line.includes('recommend') || line.includes('consider')) {
      suggestions.push({
        type: 'ai_recommendation',
        message: line.trim(),
        priority: line.includes('important') || line.includes('critical') ? 'high' : 'medium'
      });
    }
  });

  return suggestions.slice(0, 5); // Limit to top 5 suggestions
}