'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

function BasicLayout() {
  return (
    <div className="h-screen flex flex-col bg-background">
      <header className="border-b px-4 py-2 flex items-center justify-between">
        <h1 className="text-lg font-semibold">BookScribe Demo - Step 1</h1>
      </header>
      <div className="flex-1 flex overflow-hidden">
        <div className="w-80 border-r p-4">Left Panel</div>
        <div className="flex-1 p-4">Center Content</div>
        <div className="w-96 border-l p-4">Right Panel</div>
      </div>
    </div>
  )
}

function WithEditorStore() {
  const chapters = [
    { id: 'ch1', number: 1, title: 'Chapter 1', status: 'completed', wordCount: 1000 }
  ]

  return (
    <div className="h-screen flex flex-col bg-background">
      <header className="border-b px-4 py-2 flex items-center justify-between">
        <h1 className="text-lg font-semibold">BookScribe Demo - Step 2</h1>
        <Badge variant="outline">Store Initialized</Badge>
      </header>
      <div className="flex-1 flex overflow-hidden">
        <div className="w-80 border-r p-4">
          <h3 className="font-medium mb-2">Chapters</h3>
          {chapters.map(ch => (
            <div key={ch.id} className="p-2 border rounded mb-2">
              {ch.title} - {ch.wordCount} words
            </div>
          ))}
        </div>
        <div className="flex-1 p-4">Editor Content</div>
        <div className="w-96 border-l p-4">Knowledge Panel</div>
      </div>
    </div>
  )
}

function WithResizablePanels() {
  const leftWidth = 320
  const rightWidth = 384

  return (
    <div className="h-screen flex flex-col bg-background">
      <header className="border-b px-4 py-2 flex items-center justify-between">
        <h1 className="text-lg font-semibold">BookScribe Demo - Step 3</h1>
        <Badge variant="outline">Resizable Panels</Badge>
      </header>
      <div className="flex-1 flex overflow-hidden">
        <div 
          className="border-r p-4 relative"
          style={{ width: `${leftWidth}px` }}
        >
          <h3 className="font-medium mb-2">Chapters Navigator</h3>
          <div className="p-2 border rounded mb-2">Chapter 1 - 1000 words</div>
          <div className="p-2 border rounded mb-2">Chapter 2 - 1200 words</div>
        </div>
        <div className="flex-1 p-4">
          <h3 className="font-medium mb-2">Editor</h3>
          <div className="w-full h-full border rounded p-4 bg-muted/30">
            <p>Your story content goes here...</p>
          </div>
        </div>
        <div 
          className="border-l p-4 relative"
          style={{ width: `${rightWidth}px` }}
        >
          <h3 className="font-medium mb-2">Knowledge Panel</h3>
          <div className="space-y-2">
            <div className="p-2 border rounded">Characters: 3</div>
            <div className="p-2 border rounded">Locations: 5</div>
            <div className="p-2 border rounded">Plot Points: 7</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function KnowledgeDemoV2Page() {
  const [step, setStep] = useState(0)
  const [error] = useState<string | null>(null)

  const components = [
    {
      name: 'Basic Layout',
      component: BasicLayout
    },
    {
      name: 'With Editor Store',
      component: WithEditorStore
    },
    {
      name: 'With Resizable Panels',
      component: WithResizablePanels
    }
  ]

  const CurrentComponent = components[step]?.component || BasicLayout

  return (
    <div className="relative">
      {/* Navigation */}
      <div className="absolute top-4 left-4 z-10 flex gap-2">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setStep(Math.max(0, step - 1))}
          disabled={step === 0}
        >
          Previous
        </Button>
        <Badge variant="secondary" className="px-3 py-1">
          Step {step + 1} of {components.length}: {components[step]?.name}
        </Badge>
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setStep(Math.min(components.length - 1, step + 1))}
          disabled={step === components.length - 1}
        >
          Next
        </Button>
      </div>

      {/* Current Component */}
      <CurrentComponent />

      {error && (
        <div className="absolute bottom-4 left-4 right-4 z-10">
          <div className="bg-destructive/10 border border-destructive text-destructive p-4 rounded">
            Error: {error}
          </div>
        </div>
      )}
    </div>
  )
}