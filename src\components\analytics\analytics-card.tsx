"use client";

import { cn } from "@/lib/utils";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

interface AnalyticsCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon?: React.ReactNode;
  className?: string;
  valueClassName?: string;
  format?: "number" | "percent" | "duration";
}

export function AnalyticsCard({
  title,
  value,
  change,
  changeLabel,
  icon,
  className,
  valueClassName,
  format = "number",
}: AnalyticsCardProps) {
  const formatValue = (val: string | number): string => {
    if (typeof val === "string") return val;
    
    switch (format) {
      case "percent":
        return `${val}%`;
      case "duration":
        return `${val}h`;
      default:
        return val.toLocaleString();
    }
  };

  const getTrendIcon = () => {
    if (!change) return <Minus className="h-4 w-4 text-muted-foreground" />;
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
    return <TrendingDown className="h-4 w-4 text-red-600" />;
  };

  const getTrendColor = () => {
    if (!change) return "text-muted-foreground";
    return change > 0 ? "text-green-600" : "text-red-600";
  };

  return (
    <div
      className={cn(
        "rounded-lg border bg-card p-6 shadow-sm transition-shadow hover:shadow-md",
        className
      )}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </div>
      
      <div className="space-y-2">
        <p className={cn("text-2xl font-bold", valueClassName)}>
          {formatValue(value)}
        </p>
        
        {change !== undefined && (
          <div className="flex items-center gap-2">
            {getTrendIcon()}
            <span className={cn("text-sm", getTrendColor())}>
              {Math.abs(change)}%
              {changeLabel && (
                <span className="text-muted-foreground ml-1">
                  {changeLabel}
                </span>
              )}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}