"use client";

import { useState, useEffect } from "react";
import { DateRange } from "react-day-picker";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, FileText } from "lucide-react";
import { TimeRangeSelector } from "./time-range-selector";
import { AnalyticsCard } from "./analytics-card";
import { ProgressChart } from "./progress-chart";
import { HeatmapCalendar } from "./heatmap-calendar";
import { GoalTracker } from "./goal-tracker";
import { QualityMetrics } from "./quality-metrics";
import { useAnalyticsData } from "@/hooks/use-analytics-data";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Project {
  id: string;
  title: string;
  primary_genre: string;
}

interface AnalyticsDashboardProps {
  userId: string;
  projects: Project[];
}

export function AnalyticsDashboard({ userId, projects }: AnalyticsDashboardProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [selectedProject, setSelectedProject] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("overview");
  
  const { data, loading, error } = useAnalyticsData({
    userId,
    projectId: selectedProject === "all" ? undefined : selectedProject,
    dateRange,
  });

  const handleExport = (format: "pdf" | "csv") => {
    // TODO: Implement export functionality
    console.log(`Exporting as ${format}...`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">Loading analytics...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-destructive">Error loading analytics</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <TimeRangeSelector onRangeChange={setDateRange} />
          
          <Select value={selectedProject} onValueChange={setSelectedProject}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All projects" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All projects</SelectItem>
              {projects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport("csv")}
          >
            <FileText className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport("pdf")}
          >
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Main Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="quality">Quality</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <AnalyticsCard
              title="Total Words Written"
              value={data?.totalWords || 0}
              change={data?.wordsTrend}
              changeLabel="vs last period"
            />
            <AnalyticsCard
              title="Writing Streak"
              value={`${data?.currentStreak || 0} days`}
              change={data?.streakTrend}
            />
            <AnalyticsCard
              title="Average Daily Words"
              value={data?.avgDailyWords || 0}
              change={data?.avgWordsTrend}
            />
            <AnalyticsCard
              title="Active Projects"
              value={data?.activeProjects || 0}
              change={data?.projectsTrend}
            />
          </div>

          {/* Daily Word Count Chart */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Daily Word Count"
              data={data?.dailyWordCount || []}
              type="area"
              color="hsl(var(--primary))"
            />
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Writing Activity</h3>
              <HeatmapCalendar
                data={data?.heatmapData || []}
                tooltipFormatter={(item) => `${item.count} words on ${item.date}`}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          {/* Writing Patterns */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Writing by Hour of Day"
              data={data?.hourlyPattern || []}
              type="bar"
              xAxisLabel="Hour"
              yAxisLabel="Words Written"
            />
            <ProgressChart
              title="Writing by Day of Week"
              data={data?.weeklyPattern || []}
              type="bar"
              xAxisLabel="Day"
              yAxisLabel="Words Written"
            />
          </div>

          {/* Session Statistics */}
          <div className="grid gap-4 md:grid-cols-3">
            <AnalyticsCard
              title="Average Session Duration"
              value={`${data?.avgSessionDuration || 0}h`}
              format="duration"
            />
            <AnalyticsCard
              title="Words per Session"
              value={data?.avgWordsPerSession || 0}
            />
            <AnalyticsCard
              title="Total Sessions"
              value={data?.totalSessions || 0}
            />
          </div>
        </TabsContent>

        <TabsContent value="projects" className="space-y-6">
          {/* Project Progress */}
          <ProgressChart
            title="Project Progress Over Time"
            data={data?.projectProgress || []}
            multipleLines={data?.projectLines}
            showLegend
            height={400}
          />

          {/* Project Metrics */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Words by Project"
              data={data?.wordsByProject || []}
              type="bar"
            />
            <QualityMetrics
              title="Project Quality Scores"
              dimensions={data?.projectQuality || []}
              showRadar={false}
            />
          </div>
        </TabsContent>

        <TabsContent value="quality" className="space-y-6">
          {/* Overall Quality */}
          <QualityMetrics
            title="Content Quality Overview"
            dimensions={data?.qualityDimensions || []}
          />

          {/* Quality Trends */}
          <ProgressChart
            title="Quality Score Trends"
            data={data?.qualityTrends || []}
            multipleLines={data?.qualityLines}
            showLegend
          />

          {/* Improvement Areas */}
          <div className="rounded-lg border bg-card p-6">
            <h3 className="text-lg font-semibold mb-4">Areas for Improvement</h3>
            <div className="space-y-3">
              {data?.improvementAreas?.map((area, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="rounded-full bg-primary/10 p-2 text-xs font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{area.title}</p>
                    <p className="text-sm text-muted-foreground">{area.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="goals" className="space-y-6">
          {/* Active Goals */}
          <div className="grid gap-6 lg:grid-cols-2">
            <div>
              <h3 className="text-lg font-semibold mb-4">Active Goals</h3>
              <GoalTracker
                goals={data?.activeGoals || []}
              />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">Achievements</h3>
              <div className="space-y-3">
                {data?.achievements?.map((achievement, index) => (
                  <div key={index} className="flex items-center gap-3 rounded-lg border bg-card p-4">
                    <div className="text-2xl">{achievement.icon}</div>
                    <div>
                      <p className="font-medium">{achievement.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {achievement.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Goal Progress */}
          <ProgressChart
            title="Goal Progress Over Time"
            data={data?.goalProgress || []}
            type="line"
          />
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          {/* AI Insights */}
          <div className="rounded-lg border bg-card p-6">
            <h3 className="text-lg font-semibold mb-4">AI-Generated Insights</h3>
            <div className="space-y-4">
              {data?.insights?.map((insight, index) => (
                <div key={index} className="space-y-2">
                  <h4 className="font-medium">{insight.title}</h4>
                  <p className="text-sm text-muted-foreground">{insight.content}</p>
                  {insight.recommendation && (
                    <div className="rounded-md bg-primary/5 p-3">
                      <p className="text-sm font-medium">Recommendation:</p>
                      <p className="text-sm">{insight.recommendation}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Personalized Tips */}
          <div className="grid gap-4 md:grid-cols-2">
            {data?.tips?.map((tip, index) => (
              <div key={index} className="rounded-lg border bg-card p-4">
                <h4 className="font-medium mb-2">{tip.title}</h4>
                <p className="text-sm text-muted-foreground">{tip.content}</p>
              </div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}