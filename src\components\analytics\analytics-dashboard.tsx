"use client";

import { useState } from "react";
import { DateRange } from "react-day-picker";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Download, FileText } from "lucide-react";
import { TimeRangeSelector } from "./time-range-selector";
import { AnalyticsCard } from "./components/analytics-card";
import { ProgressChart } from "./components/progress-chart";
import { HeatmapCalendar } from "./components/heatmap-calendar";
import { GoalTracker } from "./components/goal-tracker";
import { QualityMetrics } from "./components/quality-metrics";
import { useAnalyticsData } from "@/hooks/use-analytics-data";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Project {
  id: string;
  title: string;
  primary_genre: string;
}

interface AnalyticsDashboardProps {
  userId: string;
  projects: Project[];
}

export function AnalyticsDashboard({ userId, projects }: AnalyticsDashboardProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [selectedProject, setSelectedProject] = useState<string>("all");
  const [activeTab, setActiveTab] = useState("overview");
  
  const { data, loading, error } = useAnalyticsData({
    userId,
    projectId: selectedProject === "all" ? undefined : selectedProject,
    dateRange,
  });

  const handleExport = (format: "pdf" | "csv") => {
    // TODO: Implement export functionality
    console.log(`Exporting as ${format}...`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">Loading analytics...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-destructive">Error loading analytics</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <TimeRangeSelector onRangeChange={setDateRange} />
          
          <Select value={selectedProject} onValueChange={setSelectedProject}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="All projects" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All projects</SelectItem>
              {projects.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport("csv")}
          >
            <FileText className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport("pdf")}
          >
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Main Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="quality">Quality</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <AnalyticsCard
              title="Total Words Written"
              value={data?.totalWords || 0}
              trend={data?.wordsTrend ? {
                value: Math.abs(data.wordsTrend),
                direction: data.wordsTrend > 0 ? 'up' : data.wordsTrend < 0 ? 'down' : 'neutral'
              } : undefined}
              subtitle="vs last period"
            />
            <AnalyticsCard
              title="Writing Streak"
              value={`${data?.currentStreak || 0} days`}
              trend={data?.streakTrend ? {
                value: Math.abs(data.streakTrend),
                direction: data.streakTrend > 0 ? 'up' : data.streakTrend < 0 ? 'down' : 'neutral'
              } : undefined}
            />
            <AnalyticsCard
              title="Average Daily Words"
              value={data?.avgDailyWords || 0}
              trend={data?.avgWordsTrend ? {
                value: Math.abs(data.avgWordsTrend),
                direction: data.avgWordsTrend > 0 ? 'up' : data.avgWordsTrend < 0 ? 'down' : 'neutral'
              } : undefined}
            />
            <AnalyticsCard
              title="Active Projects"
              value={data?.activeProjects || 0}
              trend={data?.projectsTrend ? {
                value: Math.abs(data.projectsTrend),
                direction: data.projectsTrend > 0 ? 'up' : data.projectsTrend < 0 ? 'down' : 'neutral'
              } : undefined}
            />
          </div>

          {/* Daily Word Count Chart */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Daily Word Count"
              data={data?.dailyWordCount || []}
              type="area"
              color="hsl(var(--primary))"
            />
            
            <HeatmapCalendar
              title="Writing Activity"
              data={data?.heatmapData || []}
            />
          </div>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          {/* Writing Patterns */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Writing by Hour of Day"
              data={data?.hourlyPattern || []}
              type="bar"
              xAxisLabel="Hour"
              yAxisLabel="Words Written"
            />
            <ProgressChart
              title="Writing by Day of Week"
              data={data?.weeklyPattern || []}
              type="bar"
              xAxisLabel="Day"
              yAxisLabel="Words Written"
            />
          </div>

          {/* Session Statistics */}
          <div className="grid gap-4 md:grid-cols-3">
            <AnalyticsCard
              title="Average Session Duration"
              value={`${data?.avgSessionDuration || 0}h`}
            />
            <AnalyticsCard
              title="Words per Session"
              value={data?.avgWordsPerSession || 0}
            />
            <AnalyticsCard
              title="Total Sessions"
              value={data?.totalSessions || 0}
            />
          </div>
        </TabsContent>

        <TabsContent value="projects" className="space-y-6">
          {/* Project Progress */}
          <ProgressChart
            title="Project Progress Over Time"
            data={data?.projectProgress || []}
            lines={data?.projectLines}
            showLegend
            height={400}
          />

          {/* Project Metrics */}
          <div className="grid gap-6 lg:grid-cols-2">
            <ProgressChart
              title="Words by Project"
              data={data?.wordsByProject || []}
              type="bar"
            />
            {/* TODO: Update QualityMetrics usage to match new interface */}
            {/* <QualityMetrics
              metrics={{
                readability: 80,
                consistency: 75,
                pacing: 70,
                engagement: 85,
                dialogue: 78,
                description: 82
              }}
              overallScore={78}
              showRadar={false}
            /> */}
          </div>
        </TabsContent>

        <TabsContent value="quality" className="space-y-6">
          {/* Overall Quality */}
          {/* TODO: Update QualityMetrics usage to match new interface */}
          {/* <QualityMetrics
            metrics={{
              readability: 82,
              consistency: 78,
              pacing: 75,
              engagement: 85,
              dialogue: 72,
              description: 80
            }}
            overallScore={79}
          /> */}

          {/* Quality Trends */}
          <ProgressChart
            title="Quality Score Trends"
            data={data?.qualityTrends || []}
            lines={data?.qualityLines}
            showLegend
          />

          {/* Improvement Areas */}
          <div className="rounded-lg border bg-card p-6">
            <h3 className="text-lg font-semibold mb-4">Areas for Improvement</h3>
            <div className="space-y-3">
              {data?.improvementAreas?.map((area, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="rounded-full bg-primary/10 p-2 text-xs font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{area.title}</p>
                    <p className="text-sm text-muted-foreground">{area.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="goals" className="space-y-6">
          {/* Active Goals */}
          <div className="grid gap-6 lg:grid-cols-2">
            <div>
              <h3 className="text-lg font-semibold mb-4">Active Goals</h3>
              <GoalTracker
                goals={data?.activeGoals || []}
              />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">Achievements</h3>
              <div className="space-y-3">
                {data?.achievements?.map((achievement, index) => (
                  <div key={index} className="flex items-center gap-3 rounded-lg border bg-card p-4">
                    <div className="text-2xl">{achievement.icon}</div>
                    <div>
                      <p className="font-medium">{achievement.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {achievement.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Goal Progress */}
          <ProgressChart
            title="Goal Progress Over Time"
            data={data?.goalProgress || []}
            type="line"
          />
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          {/* AI Insights */}
          <div className="rounded-lg border bg-card p-6">
            <h3 className="text-lg font-semibold mb-4">AI-Generated Insights</h3>
            <div className="space-y-4">
              {data?.insights?.map((insight, index) => (
                <div key={index} className="space-y-2">
                  <h4 className="font-medium">{insight.title}</h4>
                  <p className="text-sm text-muted-foreground">{insight.content}</p>
                  {insight.recommendation && (
                    <div className="rounded-md bg-primary/5 p-3">
                      <p className="text-sm font-medium">Recommendation:</p>
                      <p className="text-sm">{insight.recommendation}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Personalized Tips */}
          <div className="grid gap-4 md:grid-cols-2">
            {data?.tips?.map((tip, index) => (
              <div key={index} className="rounded-lg border bg-card p-4">
                <h4 className="font-medium mb-2">{tip.title}</h4>
                <p className="text-sm text-muted-foreground">{tip.content}</p>
              </div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}