import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Target, Calendar, TrendingUp, Award } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Goal {
  id: string
  type: string
  target: number
  current: number
  progress: number
  deadline: string
}

interface GoalTrackerProps {
  goals: Goal[]
  loading?: boolean
  className?: string
}

export function GoalTracker({ goals, loading = false, className }: GoalTrackerProps) {
  if (loading) {
    return (
      <div className={cn("grid gap-4", className)}>
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <Skeleton className="h-20 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const getGoalIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'daily':
        return Calendar
      case 'weekly':
        return TrendingUp
      case 'monthly':
        return Target
      default:
        return Award
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return 'bg-green-500'
    if (progress >= 75) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const getDeadlineStatus = (deadline: string) => {
    const daysLeft = Math.ceil((new Date(deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysLeft < 0) return { text: 'Overdue', color: 'destructive' }
    if (daysLeft === 0) return { text: 'Due today', color: 'warning' }
    if (daysLeft <= 3) return { text: `${daysLeft} days left`, color: 'warning' }
    return { text: `${daysLeft} days left`, color: 'secondary' }
  }

  if (goals.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Target className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
          <p className="text-muted-foreground">No active goals</p>
          <p className="text-sm text-muted-foreground mt-1">
            Set writing goals to track your progress
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {goals.map((goal) => {
        const Icon = getGoalIcon(goal.type)
        const deadlineStatus = getDeadlineStatus(goal.deadline)
        const isCompleted = goal.progress >= 100

        return (
          <Card key={goal.id} className={cn(
            "transition-all",
            isCompleted && "border-green-500/50 bg-green-500/5"
          )}>
            <CardContent className="p-4">
              <div className="space-y-3">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <div className={cn(
                      "p-2 rounded",
                      isCompleted ? "bg-green-500/10" : "bg-primary/10"
                    )}>
                      <Icon className={cn(
                        "h-4 w-4",
                        isCompleted ? "text-green-500" : "text-primary"
                      )} />
                    </div>
                    <div>
                      <p className="font-medium capitalize">{goal.type} Goal</p>
                      <p className="text-sm text-muted-foreground">
                        {goal.current.toLocaleString()} / {goal.target.toLocaleString()} words
                      </p>
                    </div>
                  </div>
                  <Badge variant={deadlineStatus.color as "destructive" | "warning" | "secondary"}>
                    {deadlineStatus.text}
                  </Badge>
                </div>

                {/* Progress */}
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Progress</span>
                    <span className={cn(
                      "font-medium",
                      isCompleted && "text-green-500"
                    )}>
                      {goal.progress}%
                    </span>
                  </div>
                  <Progress 
                    value={goal.progress} 
                    className="h-2"
                    indicatorClassName={getProgressColor(goal.progress)}
                  />
                </div>

                {/* Achievement badge */}
                {isCompleted && (
                  <div className="flex items-center gap-2 pt-1">
                    <Award className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium text-green-500">
                      Goal Achieved! 🎉
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}