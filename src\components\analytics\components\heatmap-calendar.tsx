'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@/lib/utils'
import { format, startOfWeek, addDays, isSameDay } from 'date-fns'

interface HeatmapData {
  date: string
  value: number
  words: number
}

interface HeatmapCalendarProps {
  title: string
  data: HeatmapData[]
  loading?: boolean
  months?: number
}

export function HeatmapCalendar({
  title,
  data,
  loading = false,
  months = 3
}: HeatmapCalendarProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="w-full h-[200px]" />
        </CardContent>
      </Card>
    )
  }

  // Generate calendar grid
  const endDate = new Date()
  const startDate = new Date()
  startDate.setMonth(startDate.getMonth() - months)
  
  // Adjust to start of week
  const calendarStart = startOfWeek(startDate)
  const weeks: Date[][] = []
  let currentWeek: Date[] = []
  let currentDate = calendarStart

  while (currentDate <= endDate) {
    currentWeek.push(new Date(currentDate))
    
    if (currentWeek.length === 7) {
      weeks.push(currentWeek)
      currentWeek = []
    }
    
    currentDate = addDays(currentDate, 1)
  }
  
  if (currentWeek.length > 0) {
    weeks.push(currentWeek)
  }

  // Create a map for quick data lookup
  const dataMap = new Map<string, HeatmapData>()
  data.forEach(d => {
    dataMap.set(d.date, d)
  })

  // Get color intensity based on value
  const getColor = (value: number) => {
    if (value === 0) return 'bg-muted'
    if (value < 2) return 'bg-primary/20'
    if (value < 5) return 'bg-primary/40'
    if (value < 8) return 'bg-primary/60'
    return 'bg-primary/80'
  }

  // Get month labels
  const monthLabels: { month: string; colStart: number }[] = []
  weeks[0]?.forEach((date, colIndex) => {
    weeks.forEach((week) => {
      const weekDate = week[colIndex]
      if (weekDate && weekDate.getDate() === 1) {
        monthLabels.push({
          month: format(weekDate, 'MMM'),
          colStart: colIndex
        })
      }
    })
  })

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {/* Month labels */}
          <div className="flex gap-1 text-xs text-muted-foreground ml-10">
            {monthLabels.map((label, index) => (
              <div
                key={index}
                className="flex-1"
                style={{ marginLeft: `${label.colStart * 20}px` }}
              >
                {label.month}
              </div>
            ))}
          </div>

          {/* Calendar grid */}
          <div className="flex gap-2">
            {/* Week day labels */}
            <div className="flex flex-col gap-1 text-xs text-muted-foreground">
              {weekDays.map((day, index) => (
                <div key={day} className="h-4 flex items-center">
                  {index % 2 === 1 ? day.substring(0, 1) : ''}
                </div>
              ))}
            </div>

            {/* Heatmap grid */}
            <div className="flex gap-1">
              <TooltipProvider>
                {weeks.map((week, weekIndex) => (
                  <div key={weekIndex} className="flex flex-col gap-1">
                    {week.map((date, dayIndex) => {
                      const dateStr = format(date, 'yyyy-MM-dd')
                      const dayData = dataMap.get(dateStr)
                      const isToday = isSameDay(date, new Date())
                      const isFuture = date > new Date()

                      if (isFuture) {
                        return <div key={dayIndex} className="w-4 h-4" />
                      }

                      return (
                        <Tooltip key={dayIndex}>
                          <TooltipTrigger asChild>
                            <div
                              className={cn(
                                'w-4 h-4 rounded-sm transition-all hover:ring-2 hover:ring-primary/50',
                                getColor(dayData?.value || 0),
                                isToday && 'ring-2 ring-primary'
                              )}
                            />
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-xs">
                              <p className="font-medium">{format(date, 'MMM d, yyyy')}</p>
                              <p>{dayData?.words || 0} words</p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      )
                    })}
                  </div>
                ))}
              </TooltipProvider>
            </div>
          </div>

          {/* Legend */}
          <div className="flex items-center gap-2 justify-end text-xs text-muted-foreground mt-4">
            <span>Less</span>
            <div className="flex gap-1">
              <div className="w-3 h-3 bg-muted rounded-sm" />
              <div className="w-3 h-3 bg-primary/20 rounded-sm" />
              <div className="w-3 h-3 bg-primary/40 rounded-sm" />
              <div className="w-3 h-3 bg-primary/60 rounded-sm" />
              <div className="w-3 h-3 bg-primary/80 rounded-sm" />
            </div>
            <span>More</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}