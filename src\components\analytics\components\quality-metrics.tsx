'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { RadarChart, Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer, Tooltip } from 'recharts'
import { cn } from '@/lib/utils'

interface QualityMetricsProps {
  metrics: {
    readability: number
    consistency: number
    pacing: number
    engagement: number
    dialogue: number
    description: number
  }
  overallScore: number
  loading?: boolean
  showRadar?: boolean
}

export function QualityMetrics({ 
  metrics, 
  overallScore, 
  loading = false,
  showRadar = true 
}: QualityMetricsProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Writing Quality Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  const radarData = Object.entries(metrics).map(([key, value]) => ({
    metric: key.charAt(0).toUpperCase() + key.slice(1),
    score: value,
    fullMark: 100
  }))

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-500'
    if (score >= 80) return 'text-blue-500'
    if (score >= 70) return 'text-yellow-500'
    if (score >= 60) return 'text-orange-500'
    return 'text-red-500'
  }

  const getScoreBadge = (score: number) => {
    if (score >= 90) return { text: 'Excellent', variant: 'default' as const }
    if (score >= 80) return { text: 'Good', variant: 'secondary' as const }
    if (score >= 70) return { text: 'Fair', variant: 'outline' as const }
    if (score >= 60) return { text: 'Needs Work', variant: 'outline' as const }
    return { text: 'Poor', variant: 'destructive' as const }
  }

  const overallBadge = getScoreBadge(overallScore)

  return (
    <div className="space-y-4">
      {/* Overall Score Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Overall Quality Score</p>
              <div className="flex items-baseline gap-2 mt-1">
                <span className={cn("text-3xl font-bold", getScoreColor(overallScore))}>
                  {overallScore}
                </span>
                <span className="text-muted-foreground">/100</span>
              </div>
            </div>
            <Badge variant={overallBadge.variant} className="text-sm">
              {overallBadge.text}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Quality Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          {showRadar ? (
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart data={radarData}>
                  <PolarGrid 
                    strokeDasharray="3 3"
                    stroke="hsl(var(--muted-foreground))"
                    opacity={0.3}
                  />
                  <PolarAngleAxis 
                    dataKey="metric"
                    tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <PolarRadiusAxis
                    angle={90}
                    domain={[0, 100]}
                    tick={{ fontSize: 10, fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <Radar
                    name="Score"
                    dataKey="score"
                    stroke="hsl(var(--primary))"
                    fill="hsl(var(--primary))"
                    fillOpacity={0.6}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '6px'
                    }}
                    labelStyle={{ color: 'hsl(var(--foreground))' }}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="space-y-4">
              {Object.entries(metrics).map(([key, value]) => {
                const badge = getScoreBadge(value)
                return (
                  <div key={key} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium capitalize">
                        {key}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className={cn("text-sm font-medium", getScoreColor(value))}>
                          {value}%
                        </span>
                        <Badge variant={badge.variant} className="text-xs">
                          {badge.text}
                        </Badge>
                      </div>
                    </div>
                    <Progress 
                      value={value} 
                      className="h-2"
                      indicatorClassName={cn(
                        value >= 90 && 'bg-green-500',
                        value >= 80 && value < 90 && 'bg-blue-500',
                        value >= 70 && value < 80 && 'bg-yellow-500',
                        value >= 60 && value < 70 && 'bg-orange-500',
                        value < 60 && 'bg-red-500'
                      )}
                    />
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}