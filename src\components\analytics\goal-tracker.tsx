"use client";

import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { CheckCircle2, Circle, Target } from "lucide-react";

interface Goal {
  id: string;
  title: string;
  current: number;
  target: number;
  unit: string;
  deadline?: Date;
  completed?: boolean;
}

interface GoalTrackerProps {
  goals: Goal[];
  className?: string;
}

export function GoalTracker({ goals, className }: GoalTrackerProps) {
  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const getDaysRemaining = (deadline?: Date) => {
    if (!deadline) return null;
    const today = new Date();
    const diff = deadline.getTime() - today.getTime();
    const days = Math.ceil(diff / (1000 * 60 * 60 * 24));
    return days > 0 ? days : 0;
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return "text-green-600";
    if (progress >= 75) return "text-blue-600";
    if (progress >= 50) return "text-yellow-600";
    return "text-orange-600";
  };

  return (
    <div className={cn("space-y-4", className)}>
      {goals.map((goal) => {
        const progress = calculateProgress(goal.current, goal.target);
        const daysRemaining = getDaysRemaining(goal.deadline);
        const isCompleted = goal.completed || progress >= 100;

        return (
          <div
            key={goal.id}
            className="rounded-lg border bg-card p-4 space-y-3"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                {isCompleted ? (
                  <CheckCircle2 className="h-5 w-5 text-green-600" />
                ) : (
                  <Target className="h-5 w-5 text-muted-foreground" />
                )}
                <h4 className="font-medium">{goal.title}</h4>
              </div>
              {daysRemaining !== null && !isCompleted && (
                <span className="text-sm text-muted-foreground">
                  {daysRemaining} days left
                </span>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className={cn("font-medium", getProgressColor(progress))}>
                  {goal.current.toLocaleString()} {goal.unit}
                </span>
                <span className="text-muted-foreground">
                  {goal.target.toLocaleString()} {goal.unit}
                </span>
              </div>
              <Progress value={progress} className="h-2" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{Math.round(progress)}% complete</span>
                {!isCompleted && progress > 0 && (
                  <span>
                    {(goal.target - goal.current).toLocaleString()} {goal.unit} to go
                  </span>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}