"use client";

import { useMemo } from "react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { format, eachDayOfInterval, startOfWeek, endOfWeek, getDay, subDays } from "date-fns";

interface HeatmapData {
  date: string;
  count: number;
}

interface HeatmapCalendarProps {
  data: HeatmapData[];
  endDate?: Date;
  className?: string;
  cellSize?: number;
  cellGap?: number;
  showMonthLabels?: boolean;
  showWeekdayLabels?: boolean;
  tooltipFormatter?: (data: HeatmapData) => string;
}

export function HeatmapCalendar({
  data,
  endDate = new Date(),
  className,
  cellSize = 11,
  cellGap = 2,
  showMonthLabels = true,
  showWeekdayLabels = true,
  tooltipFormatter,
}: HeatmapCalendarProps) {
  const processedData = useMemo(() => {
    const dataMap = new Map(
      data.map((item) => [format(new Date(item.date), "yyyy-MM-dd"), item.count])
    );

    const weeks = 53;
    const startDate = subDays(endDate, weeks * 7);
    const days = eachDayOfInterval({ start: startDate, end: endDate });

    const calendar: Array<Array<{ date: Date; count: number }>> = Array(7)
      .fill(null)
      .map(() => []);

    days.forEach((date) => {
      const dayOfWeek = getDay(date);
      const count = dataMap.get(format(date, "yyyy-MM-dd")) || 0;
      calendar[dayOfWeek].push({ date, count });
    });

    return calendar;
  }, [data, endDate]);

  const maxCount = useMemo(() => {
    return Math.max(...data.map((d) => d.count), 1);
  }, [data]);

  const getIntensity = (count: number): string => {
    if (count === 0) return "bg-muted";
    const intensity = Math.ceil((count / maxCount) * 4);
    switch (intensity) {
      case 1:
        return "bg-primary/20";
      case 2:
        return "bg-primary/40";
      case 3:
        return "bg-primary/60";
      case 4:
        return "bg-primary/80";
      default:
        return "bg-primary";
    }
  };

  const monthLabels = useMemo(() => {
    const labels: Array<{ month: string; x: number }> = [];
    let lastMonth = "";
    
    processedData[0].forEach((day, index) => {
      const month = format(day.date, "MMM");
      if (month !== lastMonth) {
        labels.push({ month, x: index * (cellSize + cellGap) });
        lastMonth = month;
      }
    });
    
    return labels;
  }, [processedData, cellSize, cellGap]);

  const weekdayLabels = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const defaultTooltipFormatter = (item: HeatmapData) => {
    const date = format(new Date(item.date), "MMM d, yyyy");
    return `${item.count} words on ${date}`;
  };

  const formatter = tooltipFormatter || defaultTooltipFormatter;

  return (
    <div className={cn("inline-block", className)}>
      <svg
        width={processedData[0].length * (cellSize + cellGap) + (showWeekdayLabels ? 30 : 0)}
        height={7 * (cellSize + cellGap) + (showMonthLabels ? 20 : 0) + 10}
      >
        {showMonthLabels && (
          <g transform={`translate(${showWeekdayLabels ? 30 : 0}, 10)`}>
            {monthLabels.map((label, index) => (
              <text
                key={index}
                x={label.x}
                y={0}
                className="fill-muted-foreground text-xs"
              >
                {label.month}
              </text>
            ))}
          </g>
        )}

        {showWeekdayLabels && (
          <g transform={`translate(0, ${showMonthLabels ? 30 : 10})`}>
            {weekdayLabels.map((label, index) => (
              <text
                key={index}
                x={0}
                y={index * (cellSize + cellGap) + cellSize}
                className="fill-muted-foreground text-xs"
              >
                {label}
              </text>
            ))}
          </g>
        )}

        <g transform={`translate(${showWeekdayLabels ? 30 : 0}, ${showMonthLabels ? 30 : 10})`}>
          <TooltipProvider>
            {processedData.map((week, weekIndex) => (
              <g key={weekIndex} transform={`translate(0, ${weekIndex * (cellSize + cellGap)})`}>
                {week.map((day, dayIndex) => {
                  const dateStr = format(day.date, "yyyy-MM-dd");
                  const heatmapData = { date: dateStr, count: day.count };
                  
                  return (
                    <Tooltip key={dayIndex}>
                      <TooltipTrigger asChild>
                        <rect
                          x={dayIndex * (cellSize + cellGap)}
                          y={0}
                          width={cellSize}
                          height={cellSize}
                          className={cn(
                            "transition-all cursor-pointer hover:stroke-primary hover:stroke-2",
                            getIntensity(day.count)
                          )}
                          rx={2}
                        />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-sm">{formatter(heatmapData)}</p>
                      </TooltipContent>
                    </Tooltip>
                  );
                })}
              </g>
            ))}
          </TooltipProvider>
        </g>

        <g transform={`translate(${showWeekdayLabels ? 30 : 0}, ${7 * (cellSize + cellGap) + (showMonthLabels ? 35 : 15)})`}>
          <text className="fill-muted-foreground text-xs">Less</text>
          <g transform="translate(30, -5)">
            {[0, 1, 2, 3, 4].map((level) => (
              <rect
                key={level}
                x={level * (cellSize + cellGap)}
                y={0}
                width={cellSize}
                height={cellSize}
                className={cn(
                  level === 0
                    ? "bg-muted"
                    : level === 1
                    ? "bg-primary/20"
                    : level === 2
                    ? "bg-primary/40"
                    : level === 3
                    ? "bg-primary/60"
                    : "bg-primary/80"
                )}
                rx={2}
              />
            ))}
          </g>
          <text x={100} className="fill-muted-foreground text-xs">
            More
          </text>
        </g>
      </svg>
    </div>
  );
}