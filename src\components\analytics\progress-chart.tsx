"use client";

import { useState } from "react";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type ChartType = "line" | "bar" | "area";

interface DataPoint {
  date: string;
  value: number;
  label?: string;
  [key: string]: string | number | undefined;
}

interface ProgressChartProps {
  data: DataPoint[];
  title: string;
  dataKey?: string;
  className?: string;
  height?: number;
  type?: ChartType;
  color?: string;
  showLegend?: boolean;
  yAxisLabel?: string;
  xAxisLabel?: string;
  multipleLines?: Array<{
    dataKey: string;
    name: string;
    color: string;
  }>;
}

export function ProgressChart({
  data,
  title,
  dataKey = "value",
  className,
  height = 300,
  type: initialType = "line",
  color = "hsl(var(--primary))",
  showLegend = false,
  yAxis<PERSON>abel,
  xAxisLabel,
  multipleLines,
}: ProgressChartProps) {
  const [chartType, setChartType] = useState<ChartType>(initialType);

  const renderChart = () => {
    const commonProps = {
      data,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    const axisProps = {
      stroke: "hsl(var(--muted-foreground))",
      fontSize: 12,
    };

    const gridProps = {
      strokeDasharray: "3 3",
      stroke: "hsl(var(--border))",
    };

    switch (chartType) {
      case "bar":
        return (
          <BarChart {...commonProps}>
            <CartesianGrid {...gridProps} />
            <XAxis dataKey="date" {...axisProps} />
            <YAxis {...axisProps} />
            <Tooltip
              contentStyle={{
                backgroundColor: "hsl(var(--background))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "6px",
              }}
            />
            {showLegend && <Legend />}
            {multipleLines ? (
              multipleLines.map((line) => (
                <Bar
                  key={line.dataKey}
                  dataKey={line.dataKey}
                  fill={line.color}
                  name={line.name}
                />
              ))
            ) : (
              <Bar dataKey={dataKey} fill={color} />
            )}
          </BarChart>
        );

      case "area":
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid {...gridProps} />
            <XAxis dataKey="date" {...axisProps} />
            <YAxis {...axisProps} />
            <Tooltip
              contentStyle={{
                backgroundColor: "hsl(var(--background))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "6px",
              }}
            />
            {showLegend && <Legend />}
            {multipleLines ? (
              multipleLines.map((line) => (
                <Area
                  key={line.dataKey}
                  type="monotone"
                  dataKey={line.dataKey}
                  stroke={line.color}
                  fill={line.color}
                  fillOpacity={0.6}
                  name={line.name}
                />
              ))
            ) : (
              <Area
                type="monotone"
                dataKey={dataKey}
                stroke={color}
                fill={color}
                fillOpacity={0.6}
              />
            )}
          </AreaChart>
        );

      default:
        return (
          <LineChart {...commonProps}>
            <CartesianGrid {...gridProps} />
            <XAxis dataKey="date" {...axisProps} />
            <YAxis {...axisProps} />
            <Tooltip
              contentStyle={{
                backgroundColor: "hsl(var(--background))",
                border: "1px solid hsl(var(--border))",
                borderRadius: "6px",
              }}
            />
            {showLegend && <Legend />}
            {multipleLines ? (
              multipleLines.map((line) => (
                <Line
                  key={line.dataKey}
                  type="monotone"
                  dataKey={line.dataKey}
                  stroke={line.color}
                  strokeWidth={2}
                  name={line.name}
                  dot={false}
                />
              ))
            ) : (
              <Line
                type="monotone"
                dataKey={dataKey}
                stroke={color}
                strokeWidth={2}
                dot={false}
              />
            )}
          </LineChart>
        );
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{title}</h3>
        <div className="flex gap-1">
          <Button
            variant={chartType === "line" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("line")}
          >
            Line
          </Button>
          <Button
            variant={chartType === "bar" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("bar")}
          >
            Bar
          </Button>
          <Button
            variant={chartType === "area" ? "default" : "outline"}
            size="sm"
            onClick={() => setChartType("area")}
          >
            Area
          </Button>
        </div>
      </div>

      <ResponsiveContainer width="100%" height={height}>
        {renderChart()}
      </ResponsiveContainer>

      {(xAxisLabel || yAxisLabel) && (
        <div className="flex justify-between text-xs text-muted-foreground">
          {xAxisLabel && <span>{xAxisLabel}</span>}
          {yAxisLabel && <span className="ml-auto">{yAxisLabel}</span>}
        </div>
      )}
    </div>
  );
}