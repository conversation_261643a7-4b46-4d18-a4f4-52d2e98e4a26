"use client";

import {
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ResponsiveContainer,
  Tooltip,
} from "recharts";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface QualityDimension {
  metric: string;
  score: number;
  maxScore?: number;
  description?: string;
}

interface QualityMetricsProps {
  dimensions: QualityDimension[];
  title?: string;
  className?: string;
  showRadar?: boolean;
  showBars?: boolean;
}

export function QualityMetrics({
  dimensions,
  title = "Quality Metrics",
  className,
  showRadar = true,
  showBars = true,
}: QualityMetricsProps) {
  const radarData = dimensions.map((dim) => ({
    metric: dim.metric,
    score: dim.score,
    fullMark: dim.maxScore || 100,
  }));

  const getScoreColor = (score: number, maxScore: number = 100) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-blue-600";
    if (percentage >= 40) return "text-yellow-600";
    return "text-orange-600";
  };

  const getProgressColor = (score: number, maxScore: number = 100) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return "bg-green-600";
    if (percentage >= 60) return "bg-blue-600";
    if (percentage >= 40) return "bg-yellow-600";
    return "bg-orange-600";
  };

  return (
    <div className={cn("space-y-6", className)}>
      <h3 className="text-lg font-semibold">{title}</h3>

      {showRadar && dimensions.length >= 3 && (
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={radarData}>
              <PolarGrid
                gridType="polygon"
                radialLines={true}
                stroke="hsl(var(--border))"
              />
              <PolarAngleAxis
                dataKey="metric"
                tick={{ fontSize: 12 }}
                className="text-muted-foreground"
              />
              <PolarRadiusAxis
                angle={90}
                domain={[0, 100]}
                tick={{ fontSize: 10 }}
                className="text-muted-foreground"
              />
              <Radar
                name="Score"
                dataKey="score"
                stroke="hsl(var(--primary))"
                fill="hsl(var(--primary))"
                fillOpacity={0.6}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: "hsl(var(--background))",
                  border: "1px solid hsl(var(--border))",
                  borderRadius: "6px",
                }}
              />
            </RadarChart>
          </ResponsiveContainer>
        </div>
      )}

      {showBars && (
        <div className="space-y-3">
          {dimensions.map((dimension, index) => {
            const maxScore = dimension.maxScore || 100;
            const percentage = (dimension.score / maxScore) * 100;

            return (
              <div key={index} className="space-y-1">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{dimension.metric}</span>
                  <span
                    className={cn(
                      "text-sm font-medium",
                      getScoreColor(dimension.score, maxScore)
                    )}
                  >
                    {dimension.score}/{maxScore}
                  </span>
                </div>
                <Progress
                  value={percentage}
                  className={cn("h-2", getProgressColor(dimension.score, maxScore))}
                />
                {dimension.description && (
                  <p className="text-xs text-muted-foreground">
                    {dimension.description}
                  </p>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}