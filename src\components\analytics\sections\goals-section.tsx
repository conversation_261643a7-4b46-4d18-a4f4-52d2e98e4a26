import { GoalTracker } from '../components/goal-tracker'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { <PERSON>Chart, Pie, Cell, ResponsiveContainer, Tooltip, Legend } from 'recharts'
import { Target, Plus, Trophy, TrendingUp } from 'lucide-react'

interface GoalsSectionProps {
  data: any
  isLoading: boolean
}

export function GoalsSection({ data, isLoading }: GoalsSectionProps) {
  const goals = data?.goals || {
    active: [],
    completed: 0,
    completionRate: 0
  }

  // Calculate goal statistics
  const totalGoals = goals.active.length + goals.completed
  const activeGoals = goals.active.length
  const completedThisMonth = Math.floor(goals.completed * 0.3) // Placeholder
  
  const goalStats = [
    { name: 'Completed', value: goals.completed, color: 'hsl(var(--green-500))' },
    { name: 'In Progress', value: activeGoals, color: 'hsl(var(--primary))' },
    { name: 'Overdue', value: goals.active.filter((g: any) => new Date(g.deadline) < new Date()).length, color: 'hsl(var(--destructive))' }
  ]

  return (
    <>
      {/* Goal Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Goals</p>
                <p className="text-2xl font-bold">{totalGoals}</p>
              </div>
              <Target className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold text-green-500">{goals.completed}</p>
              </div>
              <Trophy className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">{goals.completionRate}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">This Month</p>
                <p className="text-2xl font-bold">{completedThisMonth}</p>
              </div>
              <Badge variant="secondary" className="text-xs">
                +{Math.round(completedThisMonth / Math.max(1, goals.completed) * 100)}%
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Goals and Stats */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Active Goals List */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Active Goals</h3>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Goal
            </Button>
          </div>
          <GoalTracker 
            goals={goals.active.slice(0, 5)} 
            loading={isLoading}
          />
          {goals.active.length > 5 && (
            <Button variant="ghost" className="w-full mt-4">
              View All Goals ({goals.active.length})
            </Button>
          )}
        </div>

        {/* Goal Distribution Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Goal Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={goalStats}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {goalStats.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Goal Achievement Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Achievement Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { date: '2024-01-15', goal: 'Complete Chapter 10', type: 'Chapter', achieved: true },
              { date: '2024-01-10', goal: 'Write 10,000 words', type: 'Word Count', achieved: true },
              { date: '2024-01-05', goal: '7-day writing streak', type: 'Streak', achieved: true },
              { date: '2024-01-01', goal: 'Start new project', type: 'Project', achieved: true }
            ].map((achievement, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{achievement.goal}</p>
                      <p className="text-sm text-muted-foreground">{achievement.date}</p>
                    </div>
                    <Badge variant="secondary">{achievement.type}</Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Goal Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Recommended Goals</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            {[
              { 
                title: 'Daily Writing Habit', 
                description: 'Write 500 words every day for 30 days',
                difficulty: 'Medium'
              },
              { 
                title: 'Complete Current Chapter', 
                description: 'Finish Chapter 12 by end of week',
                difficulty: 'Easy'
              },
              { 
                title: 'Quality Improvement', 
                description: 'Achieve 85% quality score on next 3 chapters',
                difficulty: 'Hard'
              },
              { 
                title: 'Project Milestone', 
                description: 'Reach 50% project completion',
                difficulty: 'Medium'
              }
            ].map((recommendation, index) => (
              <div key={index} className="p-4 border rounded-lg space-y-2">
                <div className="flex items-start justify-between">
                  <h4 className="font-medium">{recommendation.title}</h4>
                  <Badge 
                    variant={
                      recommendation.difficulty === 'Easy' ? 'secondary' :
                      recommendation.difficulty === 'Medium' ? 'default' :
                      'destructive'
                    }
                    className="text-xs"
                  >
                    {recommendation.difficulty}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{recommendation.description}</p>
                <Button size="sm" variant="outline" className="w-full">
                  Set This Goal
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  )
}