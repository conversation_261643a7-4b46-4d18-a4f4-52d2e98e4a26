import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts'
import { BookOpen, ArrowRight, Star } from 'lucide-react'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface ProjectsSectionProps {
  data: any
  isLoading: boolean
}

export function ProjectsSection({ data, isLoading }: ProjectsSectionProps) {
  const projects = data?.projects || []

  // Prepare data for charts
  const projectChartData = projects.map((project: any) => ({
    name: project.title.length > 20 ? project.title.substring(0, 20) + '...' : project.title,
    words: project.wordCount,
    target: project.targetWordCount
  }))

  const projectDistribution = projects.map((project: any) => ({
    name: project.title,
    value: project.wordCount
  }))

  const COLORS = ['hsl(var(--primary))', 'hsl(var(--secondary))', 'hsl(var(--accent))', 'hsl(var(--muted))']

  if (projects.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
          <p className="text-muted-foreground">No projects to analyze</p>
          <Button asChild className="mt-4">
            <Link href="/projects/new">Create New Project</Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      {/* Project Cards Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {projects.slice(0, 6).map((project: any) => (
          <Card key={project.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-base line-clamp-1">{project.title}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    Last updated: {new Date(project.lastActivity).toLocaleDateString()}
                  </p>
                </div>
                {project.qualityScore >= 80 && (
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Progress */}
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-muted-foreground">Progress</span>
                    <span className="font-medium">{project.progress}%</span>
                  </div>
                  <Progress value={project.progress} className="h-2" />
                  <p className="text-xs text-muted-foreground mt-1">
                    {project.wordCount.toLocaleString()} / {project.targetWordCount.toLocaleString()} words
                  </p>
                </div>

                {/* Stats */}
                <div className="flex justify-between text-sm">
                  <div>
                    <span className="text-muted-foreground">Chapters</span>
                    <p className="font-medium">{project.chaptersCount}</p>
                  </div>
                  <div className="text-right">
                    <span className="text-muted-foreground">Quality</span>
                    <p className={cn(
                      "font-medium",
                      project.qualityScore >= 80 ? "text-green-500" :
                      project.qualityScore >= 60 ? "text-yellow-500" :
                      "text-red-500"
                    )}>
                      {project.qualityScore}%
                    </p>
                  </div>
                </div>

                {/* View Project Link */}
                <Button asChild variant="ghost" size="sm" className="w-full">
                  <Link href={`/projects/${project.id}`}>
                    View Project
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Word Count Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>Project Word Counts</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={projectChartData}>
                <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                <XAxis 
                  dataKey="name" 
                  className="text-xs"
                  tick={{ fill: 'hsl(var(--muted-foreground))' }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  className="text-xs"
                  tick={{ fill: 'hsl(var(--muted-foreground))' }}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  labelStyle={{ color: 'hsl(var(--foreground))' }}
                />
                <Bar dataKey="words" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Word Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Word Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={projectDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                >
                  {projectDistribution.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                  labelStyle={{ color: 'hsl(var(--foreground))' }}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Project Summary Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Project Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="text-center">
              <p className="text-2xl font-bold">{projects.length}</p>
              <p className="text-sm text-muted-foreground">Total Projects</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">
                {projects.reduce((sum: number, p: any) => sum + p.wordCount, 0).toLocaleString()}
              </p>
              <p className="text-sm text-muted-foreground">Total Words</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">
                {Math.round(projects.reduce((sum: number, p: any) => sum + p.progress, 0) / projects.length)}%
              </p>
              <p className="text-sm text-muted-foreground">Avg Completion</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">
                {Math.round(projects.reduce((sum: number, p: any) => sum + p.qualityScore, 0) / projects.length)}%
              </p>
              <p className="text-sm text-muted-foreground">Avg Quality</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}