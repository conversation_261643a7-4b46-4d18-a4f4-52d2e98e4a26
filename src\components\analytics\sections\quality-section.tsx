import { QualityMetrics } from '../components/quality-metrics'
import { ProgressChart } from '../components/progress-chart'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { TrendingUp, TrendingDown, AlertTriangle, CheckCircle } from 'lucide-react'

interface QualitySectionProps {
  data: any
  isLoading: boolean
}

export function QualitySection({ data, isLoading }: QualitySectionProps) {
  const quality = data?.quality || {
    overallScore: 0,
    metrics: {
      readability: 0,
      consistency: 0,
      pacing: 0,
      engagement: 0,
      dialogue: 0,
      description: 0
    },
    trends: []
  }

  // Find best and worst metrics
  const metricsArray = Object.entries(quality.metrics).map(([key, value]) => ({
    name: key,
    score: value as number
  }))

  const bestMetric = metricsArray.reduce((best, current) => 
    current.score > best.score ? current : best
  )
  
  const worstMetric = metricsArray.reduce((worst, current) => 
    current.score < worst.score ? current : worst
  )

  // Generate insights based on metrics
  const insights = []
  
  if (quality.metrics.readability < 70) {
    insights.push({
      type: 'warning',
      title: 'Readability Concern',
      description: 'Consider simplifying complex sentences and using more common vocabulary.'
    })
  }
  
  if (quality.metrics.pacing < 70) {
    insights.push({
      type: 'warning',
      title: 'Pacing Issues',
      description: 'Review chapter lengths and scene transitions for better flow.'
    })
  }
  
  if (quality.metrics.dialogue > 85) {
    insights.push({
      type: 'success',
      title: 'Strong Dialogue',
      description: 'Your dialogue is engaging and authentic. Keep it up!'
    })
  }

  return (
    <>
      {/* Quality Metrics Visualization */}
      <div className="grid gap-6 lg:grid-cols-2">
        <QualityMetrics
          metrics={quality.metrics}
          overallScore={quality.overallScore}
          loading={isLoading}
          showRadar={true}
        />

        {/* Quality Insights */}
        <div className="space-y-4">
          {/* Best & Worst Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Highlights</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-500/10 rounded">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="font-medium capitalize">{bestMetric.name}</span>
                </div>
                <Badge variant="default" className="bg-green-500">
                  {bestMetric.score}% - Best
                </Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-red-500/10 rounded">
                <div className="flex items-center gap-2">
                  <TrendingDown className="h-4 w-4 text-red-500" />
                  <span className="font-medium capitalize">{worstMetric.name}</span>
                </div>
                <Badge variant="destructive">
                  {worstMetric.score}% - Needs Work
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Quality Alerts */}
          <div className="space-y-3">
            {insights.map((insight, index) => (
              <Alert key={index} variant={insight.type === 'warning' ? 'destructive' : 'default'}>
                {insight.type === 'warning' ? (
                  <AlertTriangle className="h-4 w-4" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  <strong>{insight.title}:</strong> {insight.description}
                </AlertDescription>
              </Alert>
            ))}
          </div>
        </div>
      </div>

      {/* Quality Trends */}
      {quality.trends.length > 0 && (
        <ProgressChart
          title="Quality Score Trends"
          data={quality.trends}
          lines={[
            { dataKey: 'score', color: 'hsl(var(--primary))', name: 'Overall Score' }
          ]}
          type="line"
          height={250}
          loading={isLoading}
          yAxisLabel="Score"
        />
      )}

      {/* Improvement Suggestions */}
      <Card>
        <CardHeader>
          <CardTitle>Improvement Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {quality.metrics.readability < 80 && (
              <div className="p-4 border rounded-lg space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <span className="text-blue-500">📖</span> Improve Readability
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-6">
                  <li>• Use shorter sentences (aim for 15-20 words average)</li>
                  <li>• Replace complex words with simpler alternatives</li>
                  <li>• Break up long paragraphs</li>
                  <li>• Use active voice more frequently</li>
                </ul>
              </div>
            )}

            {quality.metrics.consistency < 80 && (
              <div className="p-4 border rounded-lg space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <span className="text-purple-500">🔄</span> Enhance Consistency
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-6">
                  <li>• Review character voice and behavior</li>
                  <li>• Check timeline and plot continuity</li>
                  <li>• Maintain consistent tone throughout</li>
                  <li>• Verify world-building rules are followed</li>
                </ul>
              </div>
            )}

            {quality.metrics.engagement < 80 && (
              <div className="p-4 border rounded-lg space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <span className="text-orange-500">✨</span> Boost Engagement
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-6">
                  <li>• Add more sensory details</li>
                  <li>• Increase conflict and tension</li>
                  <li>• Use stronger chapter hooks</li>
                  <li>• Show character emotions through actions</li>
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Chapter Quality Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Chapter Quality Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground py-8">
            <p>Select a specific project to view chapter-by-chapter quality analysis</p>
          </div>
        </CardContent>
      </Card>
    </>
  )
}