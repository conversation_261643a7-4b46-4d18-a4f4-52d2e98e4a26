import { loader } from '@monaco-editor/react';
import type { Monaco } from '@monaco-editor/react';
import { editor } from 'monaco-editor';
import { TypographySettings, fontFamilyMap, textSizeMap, lineHeightMap, letterSpacingMap } from '@/lib/settings/settings-types';

// Performance-optimized Monaco configuration
export function configureMonacoLoader() {
  // Only configure in browser environment
  if (typeof window === 'undefined') return;

  // Prevent loading from CDN entirely for better performance
  loader.config({ 
    monaco: undefined 
  });
  
  // Import monaco-editor directly with performance optimizations
  loader.init().then((monaco) => {
    console.log('Monaco loaded locally:', monaco);
    configureMonacoPerformance(monaco);
  }).catch((error) => {
    console.error('Failed to load Monaco locally:', error);
    // Fallback to CDN if local loading fails
    loader.config({
      paths: {
        vs: 'https://unpkg.com/monaco-editor@0.52.2/min/vs'
      }
    });
  });
}

// Configure Monaco for optimal performance
export function configureMonacoPerformance(monaco: Monaco) {
  // Configure language features for better performance
  monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.ES2015,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.CommonJS,
    noEmit: true,
    typeRoots: ['node_modules/@types']
  });

  // Disable features not needed for prose writing
  monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: true,
    noSyntaxValidation: true,
    noSuggestionDiagnostics: true
  });

  // Configure markdown language for better prose writing
  monaco.languages.setLanguageConfiguration('markdown', {
    wordPattern: /(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,
    brackets: [
      ['*', '*'],
      ['_', '_'],
      ['`', '`'],
      ['**', '**'],
      ['__', '__']
    ],
    autoClosingPairs: [
      { open: '*', close: '*' },
      { open: '_', close: '_' },
      { open: '`', close: '`' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '(', close: ')' },
      { open: '[', close: ']' }
    ],
    surroundingPairs: [
      { open: '*', close: '*' },
      { open: '_', close: '_' },
      { open: '`', close: '`' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '(', close: ')' },
      { open: '[', close: ']' }
    ]
  });

  // Register custom theme for better readability
  monaco.editor.defineTheme('literary-dark', {
    base: 'vs-dark',
    inherit: true,
    rules: [
      { token: '', foreground: 'e8e3d3' },
      { token: 'string', foreground: 'c6b6ee' },
      { token: 'comment', foreground: '7c7c7c', fontStyle: 'italic' },
      { token: 'keyword', foreground: 'd4a574' },
      { token: 'number', foreground: 'f4c2c2' },
      { token: 'regexp', foreground: 'e9ad95' },
      { token: 'type', foreground: '8cc85f' },
      { token: 'delimiter', foreground: 'd4a574' },
      { token: 'emphasis', fontStyle: 'italic' },
      { token: 'strong', fontStyle: 'bold' }
    ],
    colors: {
      'editor.background': '#0f0e0d',
      'editor.foreground': '#e8e3d3',
      'editor.lineHighlightBackground': '#1a1815',
      'editor.selectionBackground': '#d4a57440',
      'editor.selectionHighlightBackground': '#d4a57420',
      'editor.wordHighlightBackground': '#d4a57420',
      'editor.wordHighlightStrongBackground': '#d4a57440',
      'editorCursor.foreground': '#d4a574',
      'editorWhitespace.foreground': '#3a3833',
      'editorIndentGuide.background': '#3a3833',
      'editorIndentGuide.activeBackground': '#504a40'
    }
  });

  // Set default theme
  monaco.editor.setTheme('literary-dark');
}

// Optimized editor options for large documents
export function getOptimizedEditorOptions(typography?: Partial<TypographySettings>): editor.IStandaloneEditorConstructionOptions {
  // Get font settings from typography or use defaults
  const editorFont = typography?.editorFont 
    ? fontFamilyMap[typography.editorFont]
    : fontFamilyMap['jetbrains-mono'];
  
  // Get font size based on text size setting
  let fontSize = 14; // default
  if (typography?.textSize === 'custom' && typography.customTextSize) {
    fontSize = typography.customTextSize;
  } else if (typography?.textSize && typography.textSize !== 'custom') {
    const sizeConfig = textSizeMap[typography.textSize];
    if (sizeConfig) {
      fontSize = parseInt(sizeConfig.editor);
    }
  }
  
  // Calculate line height based on line height setting
  const lineHeightMultiplier = typography?.lineHeight 
    ? lineHeightMap[typography.lineHeight]
    : lineHeightMap.normal;
  const lineHeight = Math.round(fontSize * lineHeightMultiplier);
  
  // Get letter spacing from settings
  const letterSpacingStr = typography?.letterSpacing 
    ? letterSpacingMap[typography.letterSpacing]
    : letterSpacingMap.normal;
  // Convert em to pixels (approximation)
  const letterSpacing = parseFloat(letterSpacingStr) * fontSize;

  return {
    // Performance optimizations
    automaticLayout: true,
    minimap: { enabled: false }, // Disable minimap for better performance
    scrollBeyondLastLine: false,
    renderWhitespace: 'none',
    renderControlCharacters: false,
    renderLineHighlight: 'none',
    
    // Prose-specific optimizations
    wordWrap: 'bounded',
    wordWrapColumn: 80,
    lineNumbers: 'off',
    glyphMargin: false,
    folding: false,
    lineDecorationsWidth: 0,
    lineNumbersMinChars: 0,
    overviewRulerLanes: 0,
    hideCursorInOverviewRuler: true,
    
    // Disable features that slow down large documents
    quickSuggestions: false,
    suggestOnTriggerCharacters: false,
    acceptSuggestionOnCommitCharacter: false,
    tabCompletion: 'off',
    wordBasedSuggestions: 'off',
    parameterHints: { enabled: false },
    hover: { enabled: false },
    
    // Font and typography optimizations - now dynamic!
    fontFamily: editorFont,
    fontSize: fontSize,
    lineHeight: lineHeight,
    letterSpacing: letterSpacing,
    
    // Scrolling optimizations
    scrollbar: {
      vertical: 'visible',
      horizontal: 'hidden',
      verticalSliderSize: 8,
      alwaysConsumeMouseWheel: false
    },
    
    // Smooth scrolling and cursor animations
    smoothScrolling: true,
    cursorBlinking: 'phase',
    cursorSmoothCaretAnimation: 'on',
    
    // Advanced performance settings
    accessibilitySupport: 'off', // Disable unless needed
    mouseWheelZoom: false,
    multiCursorModifier: 'ctrlCmd',
    
    // Better text rendering
    padding: { top: 16, bottom: 16 },
    guides: {
      indentation: false,
      highlightActiveIndentation: false
    }
  };
}

// Optimized editor options for focus mode
export function getFocusModeEditorOptions(typography?: Partial<TypographySettings>): editor.IStandaloneEditorConstructionOptions {
  const baseOptions = getOptimizedEditorOptions(typography);
  
  // Apply focus mode specific adjustments
  let focusFontSize = 18; // default for focus mode
  if (typography?.textSize === 'custom' && typography.customTextSize) {
    focusFontSize = typography.customTextSize + 4; // Slightly larger in focus mode
  } else if (typography?.textSize && typography.textSize !== 'custom') {
    const sizeConfig = textSizeMap[typography.textSize];
    if (sizeConfig) {
      focusFontSize = parseInt(sizeConfig.editor) + 4;
    }
  }
    
  const lineHeightMultiplier = typography?.lineHeight 
    ? lineHeightMap[typography.lineHeight]
    : lineHeightMap.normal;
  const focusLineHeight = Math.round(focusFontSize * lineHeightMultiplier);
  
  return {
    ...baseOptions,
    wordWrapColumn: 100,
    fontSize: focusFontSize,
    lineHeight: focusLineHeight,
    letterSpacing: (baseOptions.letterSpacing || 0) + 0.2, // Slightly more spacing in focus mode
    padding: { top: 40, bottom: 40 },
    scrollBeyondLastLine: true,
    scrollbar: {
      ...baseOptions.scrollbar,
      vertical: 'auto',
      verticalSliderSize: 6,
    }
  };
}