"use client"

import { But<PERSON> } from '@/components/ui/button'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  BookOpen,
  PlusCircle,
  User,
  Settings,
  LogOut,
  Menu,
  Search,
  Feather
} from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/contexts/auth-context'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { Breadcrumb, BreadcrumbItem } from '@/components/ui/breadcrumb'

interface NavbarProps {
  onMenuClick?: () => void
  showMenuButton?: boolean
  breadcrumbs?: BreadcrumbItem[]
}

export function Navbar({ onMenuClick, showMenuButton = true, breadcrumbs }: NavbarProps) {
  const { user, loading, signOut } = useAuth()
  
  const handleSignOut = async () => {
    await signOut()
  }
  
  const getUserInitials = (email: string) => {
    const emailPart = email.split('@')[0]
    return emailPart ? emailPart.slice(0, 2).toUpperCase() : 'U'
  }
  
  return (
    <>
    <header className="sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/80 shadow-sm">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center space-x-4">
          {showMenuButton && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onMenuClick}
              className="md:hidden"
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          )}
          
          <Link href="/dashboard" className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80 blur-sm opacity-20" />
              <div className="relative w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-md">
                <Feather className="w-5 h-5 text-primary-foreground" />
              </div>
            </div>
            <span className="font-literary-display font-bold text-xl text-foreground">BookScribe AI</span>
          </Link>
        </div>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <Link
            href="/dashboard"
            className="text-sm font-medium transition-colors hover:text-primary text-muted-foreground hover:text-foreground"
          >
            Dashboard
          </Link>
          <Link
            href="/projects"
            className="text-sm font-medium transition-colors hover:text-primary text-muted-foreground hover:text-foreground"
          >
            Projects
          </Link>
        </nav>
        
        <div className="flex items-center space-x-2">
          {/* Search (future feature) */}
          <Button variant="ghost" size="icon" className="hidden md:flex">
            <Search className="h-4 w-4" />
            <span className="sr-only">Search</span>
          </Button>
          
          {/* Theme Toggle */}
          <ThemeToggle />
          
          {/* New Project Button */}
          <Button asChild size="sm" variant="literary" className="hidden md:flex">
            <Link href="/projects/new" className="flex items-center space-x-2">
              <Feather className="h-4 w-4" />
              <span>New Story</span>
            </Link>
          </Button>
          
          {/* Mobile New Project Button */}
          <Button asChild size="icon" variant="literary" className="md:hidden">
            <Link href="/projects/new">
              <Feather className="h-4 w-4" />
              <span className="sr-only">New Story</span>
            </Link>
          </Button>
          
          {/* User Menu */}
          {!loading && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  {user ? (
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                      {getUserInitials(user.email || '')}
                    </div>
                  ) : (
                    <User className="h-4 w-4" />
                  )}
                  <span className="sr-only">User menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {user ? (
                  <>
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user.user_metadata?.full_name || 'User'}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard" className="cursor-pointer">
                        <BookOpen className="mr-2 h-4 w-4" />
                        Dashboard
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/projects" className="cursor-pointer">
                        <BookOpen className="mr-2 h-4 w-4" />
                        My Projects
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/settings" className="cursor-pointer">
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={handleSignOut}
                      className="cursor-pointer text-red-600 focus:text-red-600"
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      Sign out
                    </DropdownMenuItem>
                  </>
                ) : (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/login" className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        Sign in
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/signup" className="cursor-pointer">
                        <PlusCircle className="mr-2 h-4 w-4" />
                        Sign up
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </header>
    {breadcrumbs && breadcrumbs.length > 0 && (
      <div className="border-b border-border bg-card/30">
        <div className="container py-3">
          <Breadcrumb items={breadcrumbs} />
        </div>
      </div>
    )}
  </>
  )
}