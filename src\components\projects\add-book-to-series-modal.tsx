'use client'

import { useState, useEffect } from 'react'
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { Loader2 } from 'lucide-react'
import type { Book } from '@/types/series'

interface AddBookToSeriesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  seriesId: string
  seriesTitle: string
  currentBookCount: number
  onSuccess?: () => void
}

export function AddBookToSeriesModal({ 
  open, 
  onOpenChange, 
  seriesId, 
  seriesTitle,
  currentBookCount,
  onSuccess 
}: AddBookToSeriesModalProps) {
  const [loading, setLoading] = useState(false)
  const [availableBooks, setAvailableBooks] = useState<Book[]>([])
  const [fetchingBooks, setFetchingBooks] = useState(false)
  const [formData, setFormData] = useState({
    projectId: '',
    bookNumber: String(currentBookCount + 1),
    bookRole: 'main',
  })
  const { toast } = useToast()

  // Fetch standalone books when modal opens
  useEffect(() => {
    if (open) {
      fetchAvailableBooks()
    }
  }, [open])

  const fetchAvailableBooks = async () => {
    setFetchingBooks(true)
    try {
      const response = await fetch('/api/projects/grouped')
      if (!response.ok) throw new Error('Failed to fetch books')
      
      const data = await response.json()
      // Only show standalone books that can be added to series
      setAvailableBooks(data.standalone || [])
    } catch (error) {
      console.error('Error fetching books:', error)
      toast({
        title: "Error",
        description: "Failed to load available books",
        variant: "destructive",
      })
    } finally {
      setFetchingBooks(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.projectId) {
      toast({
        title: "Error",
        description: "Please select a book",
        variant: "destructive",
      })
      return
    }

    if (!formData.bookNumber || parseInt(formData.bookNumber) < 1) {
      toast({
        title: "Error",
        description: "Please enter a valid book number",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch(`/api/series/${seriesId}/books`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId: formData.projectId,
          bookNumber: parseInt(formData.bookNumber),
          bookRole: formData.bookRole,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to add book to series')
      }

      toast({
        title: "Success",
        description: "Book added to series successfully",
      })
      
      onOpenChange(false)
      onSuccess?.()
      
      // Reset form
      setFormData({
        projectId: '',
        bookNumber: String(currentBookCount + 2), // Increment for next book
        bookRole: 'main',
      })
    } catch (error) {
      console.error('Error adding book to series:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add book to series",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Add Book to Series</DialogTitle>
            <DialogDescription>
              Add an existing book to "{seriesTitle}"
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="book">Select Book</Label>
              {fetchingBooks ? (
                <div className="flex items-center justify-center h-10 text-sm text-muted-foreground">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading books...
                </div>
              ) : availableBooks.length === 0 ? (
                <div className="text-sm text-muted-foreground p-3 border rounded-md">
                  No standalone books available. Create a new book first.
                </div>
              ) : (
                <Select
                  value={formData.projectId}
                  onValueChange={(value) => setFormData({ ...formData, projectId: value })}
                  disabled={loading}
                >
                  <SelectTrigger id="book">
                    <SelectValue placeholder="Choose a book" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableBooks.map((book) => (
                      <SelectItem key={book.id} value={book.id}>
                        {book.title} ({book.status.replace('_', ' ')})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="bookNumber">Book Number</Label>
                <Input
                  id="bookNumber"
                  type="number"
                  min="1"
                  value={formData.bookNumber}
                  onChange={(e) => setFormData({ ...formData, bookNumber: e.target.value })}
                  placeholder="Book order in series"
                  disabled={loading}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="bookRole">Book Role</Label>
                <Select
                  value={formData.bookRole}
                  onValueChange={(value) => setFormData({ ...formData, bookRole: value })}
                  disabled={loading}
                >
                  <SelectTrigger id="bookRole">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="main">Main</SelectItem>
                    <SelectItem value="prequel">Prequel</SelectItem>
                    <SelectItem value="sequel">Sequel</SelectItem>
                    <SelectItem value="companion">Companion</SelectItem>
                    <SelectItem value="spinoff">Spinoff</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={loading || availableBooks.length === 0}
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Add to Series
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}