'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { Loader2 } from 'lucide-react'
import type { Series } from '@/types/series'

interface EditSeriesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  series: Series | null
  onSuccess?: () => void
}

export function EditSeriesModal({ open, onOpenChange, series, onSuccess }: EditSeriesModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    genre: '',
    plannedBookCount: '',
    publicationStatus: 'planning',
  })
  const { toast } = useToast()

  // Update form when series changes
  useEffect(() => {
    if (series) {
      setFormData({
        title: series.title || '',
        description: series.description || '',
        genre: series.genre || '',
        plannedBookCount: series.planned_book_count?.toString() || '',
        publicationStatus: series.publication_status || 'planning',
      })
    }
  }, [series])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!series) return
    
    if (!formData.title.trim()) {
      toast({
        title: "Error",
        description: "Series title is required",
        variant: "destructive",
      })
      return
    }

    setLoading(true)
    
    try {
      const response = await fetch(`/api/series/${series.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || null,
          genre: formData.genre || null,
          planned_book_count: formData.plannedBookCount ? parseInt(formData.plannedBookCount) : null,
          publication_status: formData.publicationStatus,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update series')
      }

      toast({
        title: "Success",
        description: "Series updated successfully",
      })
      
      onOpenChange(false)
      onSuccess?.()
    } catch (error) {
      console.error('Error updating series:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update series",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Series</DialogTitle>
            <DialogDescription>
              Update the details of your series
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Series Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="Enter series title"
                disabled={loading}
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Brief description of the series"
                rows={3}
                disabled={loading}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="genre">Genre</Label>
                <Input
                  id="genre"
                  value={formData.genre}
                  onChange={(e) => setFormData({ ...formData, genre: e.target.value })}
                  placeholder="e.g., Fantasy, Mystery"
                  disabled={loading}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="plannedBookCount">Planned Books</Label>
                <Input
                  id="plannedBookCount"
                  type="number"
                  min="1"
                  value={formData.plannedBookCount}
                  onChange={(e) => setFormData({ ...formData, plannedBookCount: e.target.value })}
                  placeholder="Number of books"
                  disabled={loading}
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="publicationStatus">Publication Status</Label>
              <Select
                value={formData.publicationStatus}
                onValueChange={(value) => setFormData({ ...formData, publicationStatus: value })}
                disabled={loading}
              >
                <SelectTrigger id="publicationStatus">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="planning">Planning</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="hiatus">Hiatus</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Series
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}