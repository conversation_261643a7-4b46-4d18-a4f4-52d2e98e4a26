'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Folder, 
  BookOpen, 
  Plus, 
  FileText,
  AlertCircle
} from 'lucide-react'

interface BookSeries {
  id: string
  user_id: string
  name: string
  description: string | null
  created_at: string
  updated_at: string
}

interface Project {
  id: string
  title: string
  description: string | null
  primary_genre: string
  status: string
  current_word_count: number
  target_word_count: number | null
  series_id: string | null
  series_order: number | null
  created_at: string
}

interface ProjectsPageClientProps {
  userId: string
}

export function ProjectsPageClient({ userId }: ProjectsPageClientProps) {
  const [series, setSeries] = useState<BookSeries[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        
        // For now, just fetch projects since series endpoint has issues
        const projectsResponse = await fetch('/api/projects')
        if (!projectsResponse.ok) {
          throw new Error('Failed to fetch projects')
        }
        const projectsData = await projectsResponse.json()
        setProjects(projectsData.projects || [])
        
        // Extract unique series from projects
        const uniqueSeries = new Map<string, BookSeries>()
        projectsData.projects?.forEach((project: Project) => {
          if (project.series_id && !uniqueSeries.has(project.series_id)) {
            // Create a temporary series object from project data
            uniqueSeries.set(project.series_id, {
              id: project.series_id,
              user_id: userId,
              name: `Series ${project.series_id.slice(0, 8)}`, // Temporary name
              description: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
          }
        })
        setSeries(Array.from(uniqueSeries.values()))
      } catch (err) {
        console.error('Error fetching data:', err)
        setError(err instanceof Error ? err.message : 'Failed to load data')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [userId])

  // Group projects by series
  const projectsBySeries = new Map<string, Project[]>()
  const standaloneProjects: Project[] = []
  
  projects.forEach(project => {
    if (project.series_id) {
      const seriesProjects = projectsBySeries.get(project.series_id) || []
      seriesProjects.push(project)
      projectsBySeries.set(project.series_id, seriesProjects)
    } else {
      standaloneProjects.push(project)
    }
  })
  
  // Sort projects within each series by series_order
  projectsBySeries.forEach((projects) => {
    projects.sort((a, b) => (a.series_order || 0) - (b.series_order || 0))
  })

  if (loading) {
    return (
      <div className="container py-8">
        <div className="mb-8">
          <Skeleton className="h-10 w-64 mb-2" />
          <Skeleton className="h-5 w-96" />
        </div>
        <div className="space-y-6">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-72" />
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  {[1, 2, 3].map((j) => (
                    <Skeleton key={j} className="h-32" />
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container py-8">
        <Card className="border-destructive">
          <CardContent className="py-16 text-center">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-destructive" />
            <p className="text-destructive mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <>
      {/* Series Section */}
      {series.length > 0 && (
        <div className="mb-12">
          <h2 className="text-2xl font-literary-display mb-6 flex items-center gap-2">
            <Folder className="h-6 w-6" />
            Book Series
          </h2>
          <div className="space-y-6">
            {series.map((bookSeries) => {
              const seriesProjects = projectsBySeries.get(bookSeries.id) || []
              return (
                <Card key={bookSeries.id} className="border-border bg-card/50 backdrop-blur-sm">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-2xl font-literary-display">{bookSeries.name}</CardTitle>
                        <CardDescription className="text-base">
                          {bookSeries.description || 'No description yet'}
                        </CardDescription>
                      </div>
                      <Badge variant="outline">
                        {seriesProjects.length} Books
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {seriesProjects.length > 0 ? (
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {seriesProjects.map((project, index) => (
                          <Link 
                            key={project.id} 
                            href={`/projects/${project.id}/write`}
                          >
                            <Card className="h-full border-border/50 bg-background/50 transition-all hover:shadow-lg hover:border-primary/30">
                              <CardHeader className="pb-3">
                                <div className="flex items-start justify-between">
                                  <div>
                                    <p className="text-sm font-medium text-muted-foreground mb-1">
                                      Book {project.series_order || index + 1}
                                    </p>
                                    <CardTitle className="text-lg">
                                      {project.title}
                                    </CardTitle>
                                  </div>
                                  <BookOpen className="h-5 w-5 text-muted-foreground" />
                                </div>
                              </CardHeader>
                              <CardContent>
                                <Badge 
                                  variant={
                                    project.status === 'completed' ? 'default' :
                                    project.status === 'in_progress' ? 'secondary' :
                                    'outline'
                                  }
                                  className="text-xs"
                                >
                                  {project.status.replace('_', ' ')}
                                </Badge>
                              </CardContent>
                            </Card>
                          </Link>
                        ))}
                        <Link href={`/projects/new?series=${bookSeries.id}`}>
                          <Card className="h-full border-dashed border-2 border-border/50 bg-transparent hover:bg-background/50 transition-all cursor-pointer flex items-center justify-center min-h-[140px]">
                            <CardContent className="text-center py-6">
                              <Plus className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                              <p className="text-sm text-muted-foreground">Add Book to Series</p>
                            </CardContent>
                          </Card>
                        </Link>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-muted-foreground mb-4">No books in this series yet</p>
                        <Link href={`/projects/new?series=${bookSeries.id}`}>
                          <Button variant="outline" size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Add First Book
                          </Button>
                        </Link>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      )}
      
      {/* Standalone Projects Section */}
      {standaloneProjects.length > 0 && (
        <>
          {series.length > 0 && <Separator className="mb-12" />}
          <div>
            <h2 className="text-2xl font-literary-display mb-6 flex items-center gap-2">
              <FileText className="h-6 w-6" />
              Standalone Projects
            </h2>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {standaloneProjects.map((project) => (
                <Link key={project.id} href={`/projects/${project.id}/write`}>
                  <Card className="h-full border-border bg-card transition-all hover:shadow-lg hover:border-primary/30">
                    <CardHeader>
                      <CardTitle>{project.title}</CardTitle>
                      <CardDescription>
                        {project.primary_genre} • {project.target_word_count?.toLocaleString()} words
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {project.description || 'No description yet'}
                      </p>
                      <div className="mt-4 flex items-center justify-between text-sm">
                        <Badge 
                          variant={
                            project.status === 'completed' ? 'default' :
                            project.status === 'in_progress' ? 'secondary' :
                            'outline'
                          }
                        >
                          {project.status.replace('_', ' ')}
                        </Badge>
                        <span className="text-muted-foreground">
                          {project.current_word_count.toLocaleString()} / {project.target_word_count?.toLocaleString() || '?'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        </>
      )}
      
      {/* Empty State */}
      {series.length === 0 && standaloneProjects.length === 0 && (
        <Card className="text-center border-border bg-card backdrop-blur-sm">
          <CardContent className="py-16">
            <BookOpen className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
            <p className="mb-6 text-muted-foreground font-literary text-lg">
              Your literary journey begins here.
            </p>
            <div className="flex gap-3 justify-center flex-wrap">
              <Link href="/templates">
                <Button variant="outline">Browse Templates</Button>
              </Link>
              <Link href="/samples">
                <Button variant="outline">Try a Sample</Button>
              </Link>
              <Link href="/projects/new">
                <Button variant="literary">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Project
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  )
}