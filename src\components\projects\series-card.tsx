'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  ChevronDown, 
  ChevronRight, 
  BookOpen, 
  Plus, 
  MoreVertical,
  Edit,
  Trash2
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import type { Series } from '@/types/series'

interface SeriesCardProps {
  series: Series
  onAddBook?: (seriesId: string) => void
  onEditSeries?: (series: Series) => void
  onDeleteSeries?: (seriesId: string) => void
}

export function SeriesCard({ series, onAddBook, onEditSeries, onDeleteSeries }: SeriesCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  
  const completedBooks = series.books.filter(book => book.status === 'completed').length
  const totalWords = series.books.reduce((sum, book) => sum + book.current_word_count, 0)
  const targetWords = series.books.reduce((sum, book) => sum + book.target_word_count, 0)
  const progress = targetWords > 0 ? (totalWords / targetWords) * 100 : 0

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'planning': return 'secondary'
      case 'active': return 'default'
      case 'completed': return 'success'
      case 'hiatus': return 'warning'
      default: return 'secondary'
    }
  }

  const getBookStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'planning': return 'outline'
      case 'in_progress': return 'default'
      case 'completed': return 'success'
      default: return 'secondary'
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
              <CardTitle className="text-lg">{series.title}</CardTitle>
              <Badge variant={getStatusBadgeVariant(series.publication_status)}>
                {series.publication_status}
              </Badge>
            </div>
            {series.description && (
              <CardDescription className="mt-1 ml-8">
                {series.description}
              </CardDescription>
            )}
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEditSeries?.(series)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Series
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAddBook?.(series.id)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Book
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDeleteSeries?.(series.id)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Series
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-4 mt-3 ml-8 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <BookOpen className="h-3 w-3" />
            <span>{completedBooks}/{series.planned_book_count || series.books.length} books</span>
          </div>
          {series.genre && (
            <Badge variant="outline" className="text-xs">
              {series.genre}
            </Badge>
          )}
          <div className="flex-1">
            <Progress value={progress} className="h-2" />
          </div>
          <span className="text-xs">{totalWords.toLocaleString()} words</span>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="pt-0">
          <div className="space-y-2 ml-8">
            {series.books.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <p className="text-sm">No books in this series yet</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => onAddBook?.(series.id)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Book
                </Button>
              </div>
            ) : (
              <>
                {series.books.map((book) => (
                  <Link
                    key={book.id}
                    href={`/projects/${book.id}/write`}
                    className="block"
                  >
                    <div className="flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-xs font-medium">{book.book_number}</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium truncate">{book.title}</h4>
                          <Badge variant={getBookStatusBadgeVariant(book.status)} className="text-xs">
                            {book.status.replace('_', ' ')}
                          </Badge>
                          {book.book_role !== 'main' && (
                            <Badge variant="outline" className="text-xs">
                              {book.book_role}
                            </Badge>
                          )}
                        </div>
                        {book.description && (
                          <p className="text-xs text-muted-foreground truncate">
                            {book.description}
                          </p>
                        )}
                      </div>
                      <div className="text-right text-sm text-muted-foreground">
                        <div>{book.current_word_count.toLocaleString()}</div>
                        <div className="text-xs">/ {book.target_word_count.toLocaleString()}</div>
                      </div>
                    </div>
                  </Link>
                ))}
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full mt-2"
                  onClick={() => onAddBook?.(series.id)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Book
                </Button>
              </>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  )
}