'use client'

import React, { Suspense, useState, useRef, use<PERSON><PERSON>back, useEffect, Children, isValidElement, cloneElement } from 'react'
import type { ReactNode } from 'react'
import { APIErrorBoundary } from '@/components/error/api-error-boundary'
import { WizardFormSkeleton } from '@/components/loading/skeleton-loader'
import { LoadingSpinner } from '@/components/loading/loading-spinner'
import { useAPICall } from '@/hooks/use-error-handling'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { 
  AlertTriangle, 
  RefreshCw, 
  ChevronLeft, 
  ChevronRight, 
  Save,
  Wand2,
  CreditCard,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { toast } from '@/hooks/use-toast'
import { config } from '@/lib/config'

interface WizardWrapperProps {
  children: ReactNode
  currentStep: number
  totalSteps: number
  onNext?: () => void
  onPrevious?: () => void
  onSave?: () => void
  isLoading?: boolean
  canProceed?: boolean
  stepTitle?: string
  stepDescription?: string
  showProgress?: boolean
  autoSave?: boolean
}

interface WizardStepWrapperProps {
  children: ReactNode
  stepNumber: number
  title: string
  description?: string
  isActive?: boolean
  isCompleted?: boolean
  isLoading?: boolean
  error?: string | null
  onRetry?: () => void
}

function WizardErrorFallback({ 
  error, 
  retry, 
  currentStep,
  totalSteps 
}: { 
  error: Error
  retry: () => void
  currentStep: number
  totalSteps: number
}) {
  return (
    <div className="max-w-2xl mx-auto p-6">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
            <Wand2 className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle>Wizard Error</CardTitle>
          <CardDescription>
            An error occurred during the project creation process.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Progress value={(currentStep / totalSteps) * 100} className="w-full" />
          
          {error?.message && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Error Details</AlertTitle>
              <AlertDescription>{error.message}</AlertDescription>
            </Alert>
          )}
          
          <div className="flex gap-2">
            <Button onClick={retry} variant="outline" className="flex-1">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            <Button 
              onClick={() => window.history.back()} 
              variant="outline"
              className="flex-1"
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
          
          <p className="text-xs text-muted-foreground text-center">
            Your progress has been saved and you can resume later.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

function WizardLoadingFallback({ 
  currentStep, 
  totalSteps, 
  stepTitle 
}: { 
  currentStep: number
  totalSteps: number
  stepTitle?: string 
}) {
  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="space-y-6">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{Math.round((currentStep / totalSteps) * 100)}%</span>
          </div>
          <Progress value={(currentStep / totalSteps) * 100} className="w-full" />
        </div>
        
        {/* Loading content */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              <LoadingSpinner size="sm" />
              <div>
                <CardTitle>{stepTitle || 'Loading...'}</CardTitle>
                <CardDescription>Please wait while we prepare this step.</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <WizardFormSkeleton />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export function WizardWrapper({
  children,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onSave,
  isLoading = false,
  canProceed = true,
  stepTitle,
  stepDescription,
  showProgress = true,
  autoSave = true
}: WizardWrapperProps) {
  const { } = useAPICall()
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Auto-save functionality
  const performAutoSave = useCallback(async () => {
    if (!autoSave || isSaving) return

    try {
      setIsSaving(true)
      await onSave?.()
      setLastSaved(new Date())
    } catch (error) {
      console.error('Auto-save failed:', error)
      toast({
        variant: "destructive",
        title: "Auto-save Failed",
        description: "Failed to save your progress automatically.",
        duration: 3000
      })
    } finally {
      setIsSaving(false)
    }
  }, [autoSave, isSaving, onSave])

  // Trigger auto-save on form changes
  const handleFormChange = useCallback(() => {
    if (autoSave) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
      
      autoSaveTimeoutRef.current = setTimeout(() => {
        performAutoSave()
      }, 2000) // 2 second delay
    }
  }, [autoSave, performAutoSave])

  // Cleanup
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current)
      }
    }
  }, [])

  const handleRetry = useCallback(() => {
    window.location.reload()
  }, [])

  return (
    <APIErrorBoundary
      fallbackComponent={({ error, retry }) => (
        <WizardErrorFallback 
          error={error} 
          retry={retry}
          currentStep={currentStep}
          totalSteps={totalSteps}
        />
      )}
      onRetry={handleRetry}
      retryable={true}
      showErrorDetails={config.isDevelopment}
    >
      <Suspense 
        fallback={
          <WizardLoadingFallback 
            currentStep={currentStep}
            totalSteps={totalSteps}
            stepTitle={stepTitle}
          />
        }
      >
        <div className="max-w-2xl mx-auto p-6">
          <div className="space-y-6">
            {/* Progress indicator */}
            {showProgress && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Step {currentStep} of {totalSteps}</span>
                  <span>{Math.round((currentStep / totalSteps) * 100)}%</span>
                </div>
                <Progress value={(currentStep / totalSteps) * 100} className="w-full" />
              </div>
            )}

            {/* Save status */}
            {(isSaving || lastSaved) && (
              <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                {isSaving ? (
                  <>
                    <Save className="h-3 w-3 animate-spin" />
                    <span>Saving progress...</span>
                  </>
                ) : lastSaved && (
                  <>
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>Saved at {lastSaved.toLocaleTimeString()}</span>
                  </>
                )}
              </div>
            )}

            {/* Step content */}
            <Card>
              {(stepTitle || stepDescription) && (
                <CardHeader>
                  {stepTitle && <CardTitle>{stepTitle}</CardTitle>}
                  {stepDescription && <CardDescription>{stepDescription}</CardDescription>}
                </CardHeader>
              )}
              <CardContent>
                {Children.map(children, child => {
                  if (isValidElement(child)) {
                    return cloneElement(child as React.ReactElement<{ onChange?: (data: unknown) => void }>, {
                      onChange: handleFormChange
                    })
                  }
                  return child
                })}
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={onPrevious}
                disabled={currentStep === 1 || isLoading}
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex gap-2">
                {onSave && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onSave}
                    disabled={isLoading || isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Save className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Draft
                      </>
                    )}
                  </Button>
                )}

                <Button
                  type="button"
                  onClick={onNext}
                  disabled={!canProceed || isLoading}
                >
                  {isLoading ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Loading...
                    </>
                  ) : currentStep === totalSteps ? (
                    'Complete'
                  ) : (
                    <>
                      Next
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Suspense>
    </APIErrorBoundary>
  )
}

// Individual step wrapper
export function WizardStepWrapper({
  children,
  stepNumber,
  title,
  description,
  isActive = false,
  isCompleted = false,
  isLoading = false,
  error = null,
  onRetry
}: WizardStepWrapperProps) {
  return (
    <APIErrorBoundary
      fallbackComponent={({ error: stepError, retry }) => (
        <Card className="border-destructive">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-destructive text-destructive-foreground text-sm font-medium">
                <XCircle className="h-4 w-4" />
              </div>
              <div>
                <CardTitle className="text-destructive">{title} - Error</CardTitle>
                <CardDescription>
                  {stepError?.message || 'This step encountered an error.'}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Button onClick={retry || onRetry} variant="outline" size="sm">
              <RefreshCw className="h-3 w-3 mr-2" />
              Retry Step
            </Button>
          </CardContent>
        </Card>
      )}
      retryable={true}
    >
      <Card className={`transition-all ${isActive ? 'ring-2 ring-primary' : ''}`}>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className={`flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium ${
              isCompleted 
                ? 'bg-green-500 text-white' 
                : isActive 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted text-muted-foreground'
            }`}>
              {isLoading ? (
                <LoadingSpinner size="sm" />
              ) : isCompleted ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                stepNumber
              )}
            </div>
            <div className="flex-1">
              <CardTitle className={isActive ? 'text-primary' : ''}>{title}</CardTitle>
              {description && (
                <CardDescription>{description}</CardDescription>
              )}
            </div>
          </div>
        </CardHeader>
        
        {(isActive || error) && (
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            <Suspense fallback={<WizardFormSkeleton />}>
              {children}
            </Suspense>
          </CardContent>
        )}
      </Card>
    </APIErrorBoundary>
  )
}

// Payment step wrapper with specialized error handling
export function PaymentStepWrapper({
  children,
  onPaymentError
}: {
  children: ReactNode
  onPaymentError?: (error: Error) => void
}) {
  useCallback((error: Error) => {
    console.error('Payment error:', error)
    onPaymentError?.(error)
    
    toast({
      variant: "destructive",
      title: "Payment Error",
      description: "There was an issue processing your payment. Please try again.",
      duration: 5000
    })
  }, [onPaymentError])

  return (
    <APIErrorBoundary
      fallbackComponent={({ error, retry }) => (
        <Card className="border-destructive">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
              <CreditCard className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle>Payment Error</CardTitle>
            <CardDescription>
              There was an issue with the payment process.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error?.message || 'Payment processing failed'}</AlertDescription>
            </Alert>
            
            <div className="flex gap-2">
              <Button onClick={retry} variant="outline" className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button variant="outline" className="flex-1">
                Contact Support
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      retryable={true}
    >
      <Suspense fallback={
        <Card>
          <CardContent className="p-6">
            <LoadingSpinner 
              text="Loading payment form..." 
              size="lg" 
              variant="themed" 
              theme="general" 
            />
          </CardContent>
        </Card>
      }>
        {children}
      </Suspense>
    </APIErrorBoundary>
  )
}