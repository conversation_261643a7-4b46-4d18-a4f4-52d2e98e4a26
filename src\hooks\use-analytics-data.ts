import { useState, useEffect } from "react";
import { DateRange } from "react-day-picker";
import { createClient } from "@/lib/supabase/client";
import { AnalyticsEngine } from "@/lib/services/analytics-engine";
import { format, startOfDay, endOfDay } from "date-fns";

interface UseAnalyticsDataProps {
  userId: string;
  projectId?: string;
  dateRange?: DateRange;
}

interface AnalyticsData {
  // Overview metrics
  totalWords: number;
  wordsTrend: number;
  currentStreak: number;
  streakTrend: number;
  avgDailyWords: number;
  avgWordsTrend: number;
  activeProjects: number;
  projectsTrend: number;
  
  // Charts data
  dailyWordCount: Array<{ date: string; value: number }>;
  heatmapData: Array<{ date: string; count: number }>;
  hourlyPattern: Array<{ date: string; value: number }>;
  weeklyPattern: Array<{ date: string; value: number }>;
  
  // Session data
  avgSessionDuration: number;
  avgWordsPerSession: number;
  totalSessions: number;
  
  // Project data
  projectProgress: Array<{ date: string; value: number }>;
  projectLines?: Array<{ dataKey: string; name: string; color: string }>;
  wordsByProject: Array<{ date: string; value: number }>;
  projectQuality: Array<{ metric: string; score: number }>;
  
  // Quality data
  qualityDimensions: Array<{ metric: string; score: number; description: string }>;
  qualityTrends: Array<{ date: string; value: number }>;
  qualityLines?: Array<{ dataKey: string; name: string; color: string }>;
  improvementAreas: Array<{ title: string; description: string }>;
  
  // Goals data
  activeGoals: Array<{
    id: string;
    title: string;
    current: number;
    target: number;
    unit: string;
    deadline?: Date;
  }>;
  achievements: Array<{
    icon: string;
    title: string;
    description: string;
  }>;
  goalProgress: Array<{ date: string; value: number }>;
  
  // Insights
  insights: Array<{
    title: string;
    content: string;
    recommendation?: string;
  }>;
  tips: Array<{
    title: string;
    content: string;
  }>;
}

export function useAnalyticsData({
  userId,
  projectId,
  dateRange,
}: UseAnalyticsDataProps) {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);

        const supabase = createClient();
        const engine = new AnalyticsEngine(supabase);

        // Get date range
        const startDate = dateRange?.from || startOfDay(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));
        const endDate = dateRange?.to || endOfDay(new Date());

        // Fetch analytics data
        const [
          overviewMetrics,
          activityData,
          qualityData,
          goalsData,
        ] = await Promise.all([
          engine.getOverviewMetrics(userId, startDate, endDate, projectId),
          engine.getActivityData(userId, startDate, endDate, projectId),
          engine.getQualityMetrics(userId, projectId),
          engine.getGoalsData(userId, projectId),
        ]);

        // Generate mock data for demonstration
        const mockData: AnalyticsData = {
          // Overview metrics
          totalWords: overviewMetrics.totalWords || 125430,
          wordsTrend: 12.5,
          currentStreak: overviewMetrics.currentStreak || 15,
          streakTrend: 25,
          avgDailyWords: overviewMetrics.avgDailyWords || 850,
          avgWordsTrend: 8.3,
          activeProjects: overviewMetrics.activeProjects || 3,
          projectsTrend: 0,

          // Activity data
          dailyWordCount: activityData.dailyWordCount || generateDailyData(30),
          heatmapData: activityData.heatmapData || generateHeatmapData(),
          hourlyPattern: generateHourlyPattern(),
          weeklyPattern: generateWeeklyPattern(),

          // Session data
          avgSessionDuration: 2.5,
          avgWordsPerSession: 450,
          totalSessions: 278,

          // Project data
          projectProgress: generateProjectProgress(),
          projectLines: [
            { dataKey: "project1", name: "The Lost Kingdom", color: "hsl(var(--primary))" },
            { dataKey: "project2", name: "Shadow Walker", color: "hsl(var(--secondary))" },
            { dataKey: "project3", name: "Echo Chamber", color: "hsl(var(--accent))" },
          ],
          wordsByProject: [
            { date: "The Lost Kingdom", value: 45000 },
            { date: "Shadow Walker", value: 38000 },
            { date: "Echo Chamber", value: 42430 },
          ],
          projectQuality: [
            { metric: "Plot Structure", score: 85 },
            { metric: "Character Development", score: 78 },
            { metric: "Dialogue Quality", score: 82 },
            { metric: "Pacing", score: 75 },
            { metric: "World Building", score: 88 },
          ],

          // Quality data
          qualityDimensions: [
            { metric: "Readability", score: 82, description: "Clear and engaging prose" },
            { metric: "Character Depth", score: 78, description: "Well-developed characters" },
            { metric: "Plot Coherence", score: 85, description: "Logical story progression" },
            { metric: "Dialogue", score: 75, description: "Natural conversations" },
            { metric: "Pacing", score: 72, description: "Story rhythm and flow" },
          ],
          qualityTrends: generateQualityTrends(),
          qualityLines: [
            { dataKey: "readability", name: "Readability", color: "hsl(var(--primary))" },
            { dataKey: "character", name: "Character", color: "hsl(var(--secondary))" },
            { dataKey: "plot", name: "Plot", color: "hsl(var(--accent))" },
          ],
          improvementAreas: [
            {
              title: "Dialogue Authenticity",
              description: "Consider varying speech patterns between characters for better distinction",
            },
            {
              title: "Pacing in Middle Chapters",
              description: "Chapters 8-12 show slower pacing. Consider adding more conflict or tension",
            },
            {
              title: "Show vs Tell Balance",
              description: "Increase scenes that show character emotions through actions",
            },
          ],

          // Goals data
          activeGoals: [
            {
              id: "1",
              title: "Complete First Draft",
              current: 45000,
              target: 80000,
              unit: "words",
              deadline: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
            },
            {
              id: "2",
              title: "Daily Writing Goal",
              current: 850,
              target: 1000,
              unit: "words",
            },
            {
              id: "3",
              title: "Writing Streak",
              current: 15,
              target: 30,
              unit: "days",
            },
          ],
          achievements: [
            {
              icon: "🔥",
              title: "Writing Streak Master",
              description: "Maintained a 15-day writing streak",
            },
            {
              icon: "📚",
              title: "Prolific Writer",
              description: "Written over 100,000 words total",
            },
            {
              icon: "⭐",
              title: "Quality Champion",
              description: "Achieved 80+ quality score",
            },
          ],
          goalProgress: generateGoalProgress(),

          // Insights
          insights: [
            {
              title: "Peak Productivity Hours",
              content: "Your most productive writing hours are between 9 AM and 11 AM, with an average of 450 words per hour.",
              recommendation: "Schedule your most challenging writing tasks during these peak hours.",
            },
            {
              title: "Character Development Pattern",
              content: "Your secondary characters show less depth compared to protagonists, particularly in dialogue scenes.",
              recommendation: "Dedicate focused sessions to developing backstories and unique voices for supporting characters.",
            },
            {
              title: "Writing Momentum",
              content: "You write 40% more when you maintain a daily streak of 5+ days.",
              recommendation: "Focus on consistency over quantity to build sustainable writing habits.",
            },
          ],
          tips: [
            {
              title: "Try the Pomodoro Technique",
              content: "Based on your session data, 25-minute focused sprints might boost your productivity.",
            },
            {
              title: "Character Voice Exercises",
              content: "Write diary entries from each character's perspective to develop unique voices.",
            },
          ],
        };

        setData(mockData);
      } catch (err) {
        console.error("Error fetching analytics:", err);
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [userId, projectId, dateRange]);

  return { data, loading, error };
}

// Helper functions to generate mock data
function generateDailyData(days: number) {
  const data = [];
  const today = new Date();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    data.push({
      date: format(date, "MMM dd"),
      value: Math.floor(Math.random() * 1500) + 300,
    });
  }
  
  return data;
}

function generateHeatmapData() {
  const data = [];
  const today = new Date();
  
  for (let i = 364; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    data.push({
      date: format(date, "yyyy-MM-dd"),
      count: Math.random() > 0.3 ? Math.floor(Math.random() * 2000) : 0,
    });
  }
  
  return data;
}

function generateHourlyPattern() {
  return Array.from({ length: 24 }, (_, i) => ({
    date: `${i}:00`,
    value: Math.floor(Math.random() * 500) + (i >= 9 && i <= 11 ? 300 : 100),
  }));
}

function generateWeeklyPattern() {
  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  return days.map((day) => ({
    date: day,
    value: Math.floor(Math.random() * 1000) + 500,
  }));
}

function generateProjectProgress() {
  return generateDailyData(30).map((day) => ({
    ...day,
    project1: Math.floor(Math.random() * 1000) + 200,
    project2: Math.floor(Math.random() * 800) + 150,
    project3: Math.floor(Math.random() * 900) + 250,
  }));
}

function generateQualityTrends() {
  return generateDailyData(30).map((day) => ({
    ...day,
    readability: 75 + Math.floor(Math.random() * 15),
    character: 70 + Math.floor(Math.random() * 15),
    plot: 80 + Math.floor(Math.random() * 10),
  }));
}

function generateGoalProgress() {
  return generateDailyData(30).map((day, index) => ({
    ...day,
    value: 30000 + (index * 500) + Math.floor(Math.random() * 500),
  }));
}