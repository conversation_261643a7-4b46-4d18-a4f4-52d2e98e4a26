/**
 * Enhanced theme hook for BookScribe AI
 * Provides utilities for working with the multi-theme system
 */

'use client'

import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import { 
  getThemeById, 
  getDefaultTheme, 
  getAllThemes, 
  getThemesByMode,
  applyThemeToDocument,
  type ThemeDefinition 
} from '@/lib/themes/theme-registry'

export function useBookScribeTheme() {
  const { theme, setTheme, resolvedTheme, systemTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [currentThemeData, setCurrentThemeData] = useState<ThemeDefinition | null>(null)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Update current theme data when theme changes
  useEffect(() => {
    if (!mounted) return

    let themeData: ThemeDefinition | null = null

    if (theme === 'system') {
      // Use system theme
      const systemMode = systemTheme === 'dark' ? 'dark' : 'light'
      themeData = getDefaultTheme(systemMode)
    } else if (theme) {
      // Use specific theme
      themeData = getThemeById(theme)
      
      // Fallback to default if theme not found
      if (!themeData) {
        const mode = theme.includes('dark') ? 'dark' : 'light'
        themeData = getDefaultTheme(mode)
      }
    }

    setCurrentThemeData(themeData)

    // Apply theme to document if we have theme data
    if (themeData) {
      applyThemeToDocument(themeData)
    }
  }, [theme, systemTheme, mounted])

  /**
   * Get the current active theme data
   */
  const getCurrentTheme = (): ThemeDefinition | null => {
    return currentThemeData
  }

  /**
   * Get the current theme mode (light/dark)
   */
  const getCurrentMode = (): 'light' | 'dark' | null => {
    return currentThemeData?.mode || null
  }

  /**
   * Check if current theme is dark
   */
  const isDark = (): boolean => {
    return getCurrentMode() === 'dark'
  }

  /**
   * Check if current theme is light
   */
  const isLight = (): boolean => {
    return getCurrentMode() === 'light'
  }

  /**
   * Get all available themes
   */
  const getAvailableThemes = () => {
    return getAllThemes()
  }

  /**
   * Get themes by mode
   */
  const getThemesByModeHelper = (mode: 'light' | 'dark') => {
    return getThemesByMode(mode)
  }

  /**
   * Switch to a specific theme
   */
  const switchToTheme = (themeId: string) => {
    setTheme(themeId)
  }

  /**
   * Switch to next theme in the same mode
   */
  const switchToNextTheme = () => {
    if (!currentThemeData) return

    const themesInMode = getThemesByMode(currentThemeData.mode)
    const currentIndex = themesInMode.findIndex(t => t.id === currentThemeData.id)
    const nextIndex = (currentIndex + 1) % themesInMode.length
    const nextTheme = themesInMode[nextIndex]

    if (nextTheme) {
      switchToTheme(nextTheme.id)
    }
  }

  /**
   * Switch to previous theme in the same mode
   */
  const switchToPreviousTheme = () => {
    if (!currentThemeData) return

    const themesInMode = getThemesByMode(currentThemeData.mode)
    const currentIndex = themesInMode.findIndex(t => t.id === currentThemeData.id)
    const prevIndex = currentIndex === 0 ? themesInMode.length - 1 : currentIndex - 1
    const prevTheme = themesInMode[prevIndex]

    if (prevTheme) {
      switchToTheme(prevTheme.id)
    }
  }

  /**
   * Toggle between light and dark modes (keeping theme family)
   */
  const toggleMode = () => {
    if (!currentThemeData) return

    const oppositeMode = currentThemeData.mode === 'light' ? 'dark' : 'light'
    const defaultTheme = getDefaultTheme(oppositeMode)
    switchToTheme(defaultTheme.id)
  }

  /**
   * Get a color from the current theme
   */
  const getThemeColor = (colorKey: keyof ThemeDefinition['colors'], fallback: string = '#000000'): string => {
    if (!currentThemeData) return fallback
    return currentThemeData.colors[colorKey] || fallback
  }

  /**
   * Get theme display name
   */
  const getThemeDisplayName = (): string => {
    return currentThemeData?.name || 'Unknown Theme'
  }

  /**
   * Get theme description
   */
  const getThemeDescription = (): string => {
    return currentThemeData?.description || ''
  }

  /**
   * Check if a specific theme is active
   */
  const isThemeActive = (themeId: string): boolean => {
    return theme === themeId
  }

  return {
    // Basic theme state
    theme,
    setTheme,
    resolvedTheme,
    systemTheme,
    mounted,

    // Enhanced theme data
    currentTheme: currentThemeData,
    getCurrentTheme,
    getCurrentMode,
    isDark,
    isLight,

    // Theme management
    getAvailableThemes,
    getThemesByMode: getThemesByModeHelper,
    switchToTheme,
    switchToNextTheme,
    switchToPreviousTheme,
    toggleMode,

    // Theme utilities
    getThemeColor,
    getThemeDisplayName,
    getThemeDescription,
    isThemeActive,
  }
}

export type BookScribeTheme = ReturnType<typeof useBookScribeTheme>
