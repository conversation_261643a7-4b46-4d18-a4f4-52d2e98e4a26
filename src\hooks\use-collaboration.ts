'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { RealtimeChannel } from '@supabase/supabase-js'
import type { 
  CollaborationParticipantPayload,
  CollaborationChangePayload,
  CollaborationLockPayload as _ImportedCollaborationLockPayload,
  CollaborationSessionPayload as _CollaborationSessionPayload,
  CollaborationParticipant as _CollaborationParticipant
} from '@/lib/types/supabase-realtime'

interface LocalCollaborationParticipant {
  userId: string
  role: 'owner' | 'editor' | 'viewer' | 'commenter'
  status: 'online' | 'offline'
  cursor?: { line: number; column: number }
}


interface CollaborationState {
  sessionId: string | null
  participants: LocalCollaborationParticipant[]
  documentContent: string
  documentVersion: number
  isConnected: boolean
  locks: Map<string, { userId: string; timestamp: number }>
}

interface UseCollaborationOptions {
  sessionId: string
  userId: string
  onDocumentChange?: (content: string, version: number) => void
  onParticipantChange?: (participants: LocalCollaborationParticipant[]) => void
  onCursorMove?: (userId: string, position: { line: number; column: number }) => void
  onError?: (error: Error) => void
}

export function useCollaboration({
  sessionId,
  userId,
  onDocumentChange,
  onParticipantChange,
  onCursorMove,
  onError
}: UseCollaborationOptions) {
  const [state, setState] = useState<CollaborationState>({
    sessionId: null,
    participants: [],
    documentContent: '',
    documentVersion: 0,
    isConnected: false,
    locks: new Map()
  })

  const channelRef = useRef<RealtimeChannel | null>(null)
  const supabase = createClient()
  const stateRef = useRef(state)
  
  // Keep state ref updated
  useEffect(() => {
    stateRef.current = state
  }, [state])

  // Set up real-time subscriptions
  const setupRealtimeSubscriptions = useCallback(() => {
    const channel = supabase
      .channel(`collaboration:${sessionId}`)
      // Listen to participant changes
      .on(
        'postgres_changes' as const,
        {
          event: '*',
          schema: 'public',
          table: 'collaboration_participants',
          filter: `session_id=eq.${sessionId}`
        },
        (payload: CollaborationParticipantPayload) => {
          if (payload.eventType === 'UPDATE') {
            setState(prev => ({
              ...prev,
              participants: prev.participants.map(p =>
                p.userId === payload.new.user_id
                  ? {
                      ...p,
                      status: payload.new.status,
                      cursor: payload.new.cursor_position
                    }
                  : p
              )
            }))

            if (payload.new.cursor_position && payload.new.user_id !== userId) {
              onCursorMove?.(payload.new.user_id, payload.new.cursor_position)
            }
          }
        }
      )
      // Listen to document changes
      .on(
        'postgres_changes' as const,
        {
          event: 'INSERT',
          schema: 'public',
          table: 'collaboration_changes',
          filter: `session_id=eq.${sessionId}`
        },
        async (_payload: CollaborationChangePayload) => {
          // Fetch updated document content
          const { data } = await supabase
            .from('collaboration_sessions')
            .select('document_content, document_version')
            .eq('id', sessionId)
            .single()

          if (data) {
            setState(prev => ({
              ...prev,
              documentContent: data.document_content,
              documentVersion: data.document_version
            }))

            onDocumentChange?.(data.document_content, data.document_version)
          }
        }
      )
      // Listen to lock changes
      // TODO: Fix Supabase event type compatibility
      // .on(
      //   'postgres_changes' as const,
      //   {
      //     event: '*',
      //     schema: 'public',
      //     table: 'collaboration_locks',
      //     filter: `session_id=eq.${sessionId}`
      //   },
      //   (payload: CollaborationLockPayload) => {
      //     if (payload.eventType === 'INSERT' && payload.new?.section && payload.new?.user_id) {
      //       setState(prev => ({
      //         ...prev,
      //         locks: new Map(prev.locks).set(payload.new!.section, {
      //           userId: payload.new!.user_id,
      //           timestamp: Date.now()
      //         })
      //       }))
      //     } else if (payload.eventType === 'DELETE' && payload.old?.section) {
      //       setState(prev => {
      //         const newLocks = new Map(prev.locks)
      //         newLocks.delete(payload.old!.section)
      //         return { ...prev, locks: newLocks }
      //       })
      //     }
      //   }
      // )
      // Use Presence for real-time user tracking
      .on('presence', { event: 'sync' }, () => {
        const presenceState = channel.presenceState()
        const onlineUserIds: string[] = []

        // Extract user IDs from presence state
        Object.keys(presenceState).forEach(key => {
          const presences = presenceState[key]
          if (Array.isArray(presences) && presences.length > 0) {
            const presence = presences[0]
            if (presence && typeof presence === 'object' && 'userId' in presence) {
              onlineUserIds.push(presence.userId as string)
            }
          }
        })

        setState(prev => ({
          ...prev,
          participants: prev.participants.map(p => ({
            ...p,
            status: onlineUserIds.includes(p.userId) ? 'online' : 'offline'
          }))
        }))

        if (onParticipantChange) {
          onParticipantChange(
            stateRef.current.participants.filter(p => onlineUserIds.includes(p.userId))
          )
        }
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          // Track presence
          await channel.track({
            userId,
            online_at: new Date().toISOString()
          })
        }
      })

    channelRef.current = channel
  }, [sessionId, userId, supabase, onDocumentChange, onParticipantChange, onCursorMove])

  // Join session
  const joinSession = useCallback(async (role: 'editor' | 'viewer' | 'commenter' = 'viewer') => {
    try {
      const response = await fetch('/api/collaboration/join', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, userId, role })
      })

      if (!response.ok) throw new Error('Failed to join session')

      const data = await response.json()
      
      setState(prev => ({
        ...prev,
        sessionId,
        participants: data.participants,
        documentContent: data.documentContent,
        documentVersion: data.documentVersion,
        isConnected: true
      }))

      // Set up real-time subscriptions
      setupRealtimeSubscriptions()

      return data
    } catch (error) {
      onError?.(error as Error)
      throw error
    }
  }, [sessionId, userId, setupRealtimeSubscriptions, onError])

  // Leave session
  const leaveSession = useCallback(async () => {
    try {
      await fetch('/api/collaboration/leave', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, userId })
      })

      // Clean up subscriptions
      if (channelRef.current) {
        await supabase.removeChannel(channelRef.current)
        channelRef.current = null
      }

      setState({
        sessionId: null,
        participants: [],
        documentContent: '',
        documentVersion: 0,
        isConnected: false,
        locks: new Map()
      })
    } catch (error) {
      onError?.(error as Error)
    }
  }, [sessionId, userId, supabase, onError])

  // Apply document change
  const applyChange = useCallback(async (change: {
    type: 'insert' | 'delete' | 'format'
    position: { line: number; column: number }
    content?: string
    length?: number
  }) => {
    try {
      const response = await fetch('/api/collaboration/change', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, userId, change })
      })

      if (!response.ok) throw new Error('Failed to apply change')

      const { version } = await response.json()
      
      setState(prev => ({
        ...prev,
        documentVersion: version
      }))

      return version
    } catch (error) {
      onError?.(error as Error)
      throw error
    }
  }, [sessionId, userId, onError])

  // Update cursor position
  const updateCursor = useCallback(async (position: { line: number; column: number }) => {
    try {
      await fetch('/api/collaboration/cursor', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, userId, position })
      })
    } catch (error) {
      // Cursor updates are non-critical
      console.error('Failed to update cursor:', error)
    }
  }, [sessionId, userId])

  // Lock section
  const lockSection = useCallback(async (section: string) => {
    try {
      const response = await fetch('/api/collaboration/lock', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, userId, section })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to lock section')
      }

      setState(prev => ({
        ...prev,
        locks: new Map(prev.locks).set(section, { userId, timestamp: Date.now() })
      }))

      return true
    } catch (error) {
      onError?.(error as Error)
      return false
    }
  }, [sessionId, userId, onError])

  // Unlock section
  const unlockSection = useCallback(async (section: string) => {
    try {
      await fetch('/api/collaboration/unlock', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, userId, section })
      })

      setState(prev => {
        const newLocks = new Map(prev.locks)
        newLocks.delete(section)
        return { ...prev, locks: newLocks }
      })
    } catch (error) {
      onError?.(error as Error)
    }
  }, [sessionId, userId, onError])



  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current)
      }
    }
  }, [supabase])

  return {
    ...state,
    joinSession,
    leaveSession,
    applyChange,
    updateCursor,
    lockSection,
    unlockSection,
    isLocked: (section: string) => state.locks.has(section),
    canEdit: () => {
      const participant = state.participants.find(p => p.userId === userId)
      return participant?.role === 'owner' || participant?.role === 'editor'
    }
  }
}