import { useEffect, useCallback, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'

export interface KeyboardShortcut {
  key: string
  ctrl?: boolean
  alt?: boolean
  shift?: boolean
  meta?: boolean
  description: string
  action: () => void
  category?: 'navigation' | 'creation' | 'editing' | 'general'
}

const globalShortcuts: KeyboardShortcut[] = [
  {
    key: 'k',
    ctrl: true,
    description: 'Open command menu',
    category: 'general',
    action: () => {
      // This would open a command palette in the future
      console.log('Command menu opened')
    }
  },
  {
    key: '/',
    ctrl: true,
    description: 'Focus search',
    category: 'general',
    action: () => {
      const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
      }
    }
  },
  {
    key: '?',
    shift: true,
    description: 'Show keyboard shortcuts',
    category: 'general',
    action: () => {
      // Dispatch event to show shortcuts modal
      const event = new CustomEvent('show-keyboard-shortcuts')
      window.dispatchEvent(event)
    }
  },
]

export function useKeyboardShortcuts(customShortcuts: KeyboardShortcut[] = []) {
  // const router = useRouter() // Commented out as not currently used
  const { toast: _toast } = useToast()
  
  const shortcuts = useMemo(() => [...globalShortcuts, ...customShortcuts], [customShortcuts])
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in inputs
    if (
      event.target instanceof HTMLInputElement ||
      event.target instanceof HTMLTextAreaElement ||
      event.target instanceof HTMLSelectElement ||
      (event.target as HTMLElement).contentEditable === 'true'
    ) {
      return
    }
    
    shortcuts.forEach(shortcut => {
      const ctrlMatch = shortcut.ctrl ? (event.ctrlKey || event.metaKey) : (!shortcut.ctrl && !event.ctrlKey && !event.metaKey)
      const altMatch = shortcut.alt ? event.altKey : !event.altKey
      const shiftMatch = shortcut.shift ? event.shiftKey : !event.shiftKey
      
      if (
        event.key.toLowerCase() === shortcut.key.toLowerCase() &&
        ctrlMatch &&
        altMatch &&
        shiftMatch
      ) {
        event.preventDefault()
        shortcut.action()
      }
    })
  }, [shortcuts])
  
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])
  
  return shortcuts
}

// Enhanced writing-specific shortcuts for power users
export function useWritingShortcuts(
  onSave?: () => void,
  onUndo?: () => void,
  onRedo?: () => void,
  onToggleFocusMode?: () => void,
  onToggleAI?: () => void,
  onToggleChapterNav?: () => void,
  onToggleStoryBible?: () => void,
  onToggleStats?: () => void,
  onFormatBold?: () => void,
  onFormatItalic?: () => void,
  onFormatUnderline?: () => void,
  onNewChapter?: () => void,
  onQuickExport?: () => void,
  onFind?: () => void,
  onReplace?: () => void,
  onGoToLine?: () => void,
  onSelectAll?: () => void,
  onDuplicateLine?: () => void,
  onDeleteLine?: () => void,
  onMoveLineUp?: () => void,
  onMoveLineDown?: () => void,
  onToggleComment?: () => void,
  onZoomIn?: () => void,
  onZoomOut?: () => void,
  onZoomReset?: () => void
) {
  const shortcuts: KeyboardShortcut[] = [
    // File operations
    {
      key: 's',
      ctrl: true,
      description: 'Save document',
      category: 'editing',
      action: () => onSave?.()
    },
    {
      key: 'n',
      ctrl: true,
      shift: true,
      description: 'New chapter',
      category: 'creation',
      action: () => onNewChapter?.()
    },
    {
      key: 'e',
      ctrl: true,
      description: 'Quick export',
      category: 'general',
      action: () => onQuickExport?.()
    },

    // Edit operations
    {
      key: 'z',
      ctrl: true,
      description: 'Undo',
      category: 'editing',
      action: () => onUndo?.()
    },
    {
      key: 'z',
      ctrl: true,
      shift: true,
      description: 'Redo',
      category: 'editing',
      action: () => onRedo?.()
    },
    {
      key: 'y',
      ctrl: true,
      description: 'Redo (alternative)',
      category: 'editing',
      action: () => onRedo?.()
    },
    {
      key: 'a',
      ctrl: true,
      description: 'Select all',
      category: 'editing',
      action: () => onSelectAll?.()
    },
    {
      key: 'd',
      ctrl: true,
      description: 'Duplicate line',
      category: 'editing',
      action: () => onDuplicateLine?.()
    },
    {
      key: 'k',
      ctrl: true,
      shift: true,
      description: 'Delete line',
      category: 'editing',
      action: () => onDeleteLine?.()
    },

    // Text formatting
    {
      key: 'b',
      ctrl: true,
      description: 'Bold text',
      category: 'editing',
      action: () => onFormatBold?.()
    },
    {
      key: 'i',
      ctrl: true,
      description: 'Italic text',
      category: 'editing',
      action: () => onFormatItalic?.()
    },
    {
      key: 'u',
      ctrl: true,
      description: 'Underline text',
      category: 'editing',
      action: () => onFormatUnderline?.()
    },
    {
      key: '/',
      ctrl: true,
      description: 'Toggle comment',
      category: 'editing',
      action: () => onToggleComment?.()
    },

    // Find and replace
    {
      key: 'f',
      ctrl: true,
      description: 'Find in document',
      category: 'navigation',
      action: () => onFind?.()
    },
    {
      key: 'h',
      ctrl: true,
      description: 'Find and replace',
      category: 'editing',
      action: () => onReplace?.()
    },
    {
      key: 'g',
      ctrl: true,
      description: 'Go to line',
      category: 'navigation',
      action: () => onGoToLine?.()
    },

    // Line operations
    {
      key: 'ArrowUp',
      alt: true,
      description: 'Move line up',
      category: 'editing',
      action: () => onMoveLineUp?.()
    },
    {
      key: 'ArrowDown',
      alt: true,
      description: 'Move line down',
      category: 'editing',
      action: () => onMoveLineDown?.()
    },

    // View and panels
    {
      key: 'f11',
      description: 'Toggle focus mode',
      category: 'navigation',
      action: () => onToggleFocusMode?.()
    },
    {
      key: 'Escape',
      description: 'Exit focus mode',
      category: 'navigation',
      action: () => onToggleFocusMode?.()
    },
    {
      key: '1',
      ctrl: true,
      description: 'Toggle chapter navigator',
      category: 'navigation',
      action: () => onToggleChapterNav?.()
    },
    {
      key: '2',
      ctrl: true,
      description: 'Toggle story bible',
      category: 'navigation',
      action: () => onToggleStoryBible?.()
    },
    {
      key: '3',
      ctrl: true,
      description: 'Toggle AI assistant',
      category: 'navigation',
      action: () => onToggleAI?.()
    },
    {
      key: '5',
      ctrl: true,
      description: 'Toggle writing stats',
      category: 'navigation',
      action: () => onToggleStats?.()
    },

    // Zoom operations
    {
      key: '=',
      ctrl: true,
      description: 'Zoom in',
      category: 'general',
      action: () => onZoomIn?.()
    },
    {
      key: '-',
      ctrl: true,
      description: 'Zoom out',
      category: 'general',
      action: () => onZoomOut?.()
    },
    {
      key: '0',
      ctrl: true,
      description: 'Reset zoom',
      category: 'general',
      action: () => onZoomReset?.()
    },

    // Advanced AI shortcuts
    {
      key: 'j',
      ctrl: true,
      shift: true,
      description: 'AI content generation',
      category: 'creation',
      action: () => onToggleAI?.()
    },
    {
      key: 'r',
      ctrl: true,
      shift: true,
      description: 'AI rewrite selection',
      category: 'editing',
      action: () => {
        // Trigger AI rewrite for selected text
        const event = new CustomEvent('ai-rewrite-selection');
        window.dispatchEvent(event);
      }
    },
    {
      key: 't',
      ctrl: true,
      shift: true,
      description: 'AI continue writing',
      category: 'creation',
      action: () => {
        // Trigger AI continue writing
        const event = new CustomEvent('ai-continue-writing');
        window.dispatchEvent(event);
      }
    },

    // Quick actions
    {
      key: 'Enter',
      ctrl: true,
      description: 'Quick save and continue',
      category: 'general',
      action: () => {
        onSave?.();
        // Focus back to editor after save
        setTimeout(() => {
          const editor = document.querySelector('.monaco-editor textarea') as HTMLElement;
          editor?.focus();
        }, 100);
      }
    },
    {
      key: 'w',
      ctrl: true,
      description: 'Word count popup',
      category: 'general',
      action: () => {
        const event = new CustomEvent('show-word-count');
        window.dispatchEvent(event);
      }
    },
  ]
  
  return useKeyboardShortcuts(shortcuts)
}

// Enhanced dashboard shortcuts
export function useDashboardShortcuts() {
  const router = useRouter()
  
  const shortcuts: KeyboardShortcut[] = [
    // Project management
    {
      key: 'n',
      ctrl: true,
      description: 'Create new project',
      category: 'creation',
      action: () => {
        router.push('/projects/new')
      }
    },
    {
      key: 'p',
      ctrl: true,
      description: 'View all projects',
      category: 'navigation',
      action: () => {
        router.push('/dashboard')
      }
    },
    {
      key: 't',
      ctrl: true,
      description: 'Browse templates',
      category: 'navigation',
      action: () => {
        router.push('/templates')
      }
    },
    {
      key: 'd',
      ctrl: true,
      shift: true,
      description: 'Go to demo',
      category: 'navigation',
      action: () => {
        router.push('/demo')
      }
    },
    {
      key: 'h',
      ctrl: true,
      shift: true,
      description: 'Go to home',
      category: 'navigation',
      action: () => {
        router.push('/')
      }
    },

    // Search and filtering
    {
      key: 'f',
      ctrl: true,
      description: 'Focus project search',
      category: 'navigation',
      action: () => {
        const searchInput = document.querySelector('[data-search-projects]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }
    },
    {
      key: 'r',
      ctrl: true,
      description: 'Refresh project list',
      category: 'general',
      action: () => {
        window.location.reload();
      }
    },

    // Quick actions
    {
      key: 'Enter',
      description: 'Open selected project',
      category: 'navigation',
      action: () => {
        const selectedProject = document.querySelector('[data-project-selected]') as HTMLElement;
        if (selectedProject) {
          selectedProject.click();
        }
      }
    },
    {
      key: 'Delete',
      description: 'Delete selected project',
      category: 'general',
      action: () => {
        const event = new CustomEvent('delete-selected-project');
        window.dispatchEvent(event);
      }
    },

    // View switching
    {
      key: '1',
      ctrl: true,
      description: 'Grid view',
      category: 'navigation',
      action: () => {
        const event = new CustomEvent('switch-view', { detail: 'grid' });
        window.dispatchEvent(event);
      }
    },
    {
      key: '2',
      ctrl: true,
      description: 'List view',
      category: 'navigation',
      action: () => {
        const event = new CustomEvent('switch-view', { detail: 'list' });
        window.dispatchEvent(event);
      }
    },
    {
      key: '3',
      ctrl: true,
      description: 'Table view',
      category: 'navigation',
      action: () => {
        const event = new CustomEvent('switch-view', { detail: 'table' });
        window.dispatchEvent(event);
      }
    },

    // Sorting
    {
      key: 's',
      ctrl: true,
      shift: true,
      description: 'Sort by created date',
      category: 'general',
      action: () => {
        const event = new CustomEvent('sort-projects', { detail: 'created' });
        window.dispatchEvent(event);
      }
    },
    {
      key: 'm',
      ctrl: true,
      shift: true,
      description: 'Sort by modified date',
      category: 'general',
      action: () => {
        const event = new CustomEvent('sort-projects', { detail: 'modified' });
        window.dispatchEvent(event);
      }
    },
    {
      key: 'w',
      ctrl: true,
      shift: true,
      description: 'Sort by word count',
      category: 'general',
      action: () => {
        const event = new CustomEvent('sort-projects', { detail: 'wordcount' });
        window.dispatchEvent(event);
      }
    },
  ]
  
  return useKeyboardShortcuts(shortcuts)
}

// Project-specific shortcuts for project overview pages
export function useProjectShortcuts(projectId: string) {
  const router = useRouter()
  
  const shortcuts: KeyboardShortcut[] = [
    // Navigation within project
    {
      key: 'w',
      ctrl: true,
      description: 'Go to write page',
      category: 'navigation',
      action: () => {
        router.push(`/projects/${projectId}/write`)
      }
    },
    {
      key: 'o',
      ctrl: true,
      description: 'Go to overview',
      category: 'navigation',
      action: () => {
        router.push(`/projects/${projectId}/overview`)
      }
    },
    {
      key: 'b',
      ctrl: true,
      shift: true,
      description: 'Go to story bible',
      category: 'navigation',
      action: () => {
        router.push(`/projects/${projectId}/story-bible`)
      }
    },
    {
      key: 'd',
      ctrl: true,
      description: 'Go to dashboard',
      category: 'navigation',
      action: () => {
        router.push(`/projects/${projectId}/dashboard`)
      }
    },

    // Quick actions
    {
      key: 'e',
      ctrl: true,
      description: 'Quick export',
      category: 'general',
      action: () => {
        const exportButton = document.querySelector('[data-quick-export]') as HTMLElement;
        if (exportButton) {
          exportButton.click();
        }
      }
    },
    {
      key: 'g',
      ctrl: true,
      shift: true,
      description: 'Generate structure',
      category: 'creation',
      action: () => {
        const generateButton = document.querySelector('[data-generate-structure]') as HTMLElement;
        if (generateButton) {
          generateButton.click();
        }
      }
    },
    {
      key: 'c',
      ctrl: true,
      shift: true,
      description: 'Generate chapter',
      category: 'creation',
      action: () => {
        const generateButton = document.querySelector('[data-generate-chapter]') as HTMLElement;
        if (generateButton) {
          generateButton.click();
        }
      }
    },

    // Back navigation
    {
      key: 'ArrowLeft',
      alt: true,
      description: 'Go back',
      category: 'navigation',
      action: () => {
        window.history.back();
      }
    },
    {
      key: 'ArrowRight',
      alt: true,
      description: 'Go forward',
      category: 'navigation',
      action: () => {
        window.history.forward();
      }
    },
  ]
  
  return useKeyboardShortcuts(shortcuts)
}