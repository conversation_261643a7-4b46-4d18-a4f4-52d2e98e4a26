/**
 * Simplified Theme Hook for BookScribe AI
 * Provides easy theme switching with direct CSS control
 */

'use client';

import { useState, useEffect } from 'react';
import { 
  ThemeId, 
  themeConfigs, 
  applyTheme as _applyTheme, 
  getCurrentTheme, 
  switchTheme, 
  getAllThemes, 
  getThemesByMode,
  initializeTheme 
} from '@/lib/themes/theme-applier';

export function useSimpleTheme() {
  const [currentTheme, setCurrentTheme] = useState<ThemeId | null>(() => {
    // Initialize with current theme to prevent flicker
    if (typeof document !== 'undefined') {
      return getCurrentTheme();
    }
    return null;
  });
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Initialize theme if not already done
    if (!currentTheme) {
      initializeTheme();
      setCurrentTheme(getCurrentTheme());
    }
    setMounted(true);
  }, [currentTheme]);

  const handleSwitchTheme = (themeId: ThemeId) => {
    switchTheme(themeId);
    setCurrentTheme(themeId);
  };

  const isThemeActive = (themeId: ThemeId): boolean => {
    return currentTheme === themeId;
  };

  const getCurrentMode = () => {
    if (!currentTheme) return null;
    return themeConfigs[currentTheme]?.mode || null;
  };

  return {
    // State
    currentTheme,
    mounted,
    
    // Theme data
    themeConfigs,
    getAllThemes,
    getThemesByMode,
    
    // Actions
    switchTheme: handleSwitchTheme,
    isThemeActive,
    getCurrentMode,
    
    // Utilities
    isDark: () => getCurrentMode() === 'dark',
    isLight: () => getCurrentMode() === 'light',
  };
}