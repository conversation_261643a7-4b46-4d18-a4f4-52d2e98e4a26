import OpenAI from 'openai';
type ChatCompletionMessageParam = OpenAI.Chat.Completions.ChatCompletionMessageParam;
type ChatCompletionTool = OpenAI.Chat.Completions.ChatCompletionTool;
import { BookContext } from './types';
import { config } from '@/lib/config';
import { AI_TEMPERATURE, AI_MAX_TOKENS, AI_MODELS } from '../config/ai-settings';
import { withRetry, ErrorContext } from '../services/error-handler';
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from '../services/circuit-breaker';
import { supportsStructuredOutputs, createJsonObjectFallback } from '../utils/structured-outputs';
import { z } from 'zod';

export abstract class BaseAgent {
  protected openai: OpenAI;
  protected context: BookContext;

  constructor(context: BookContext) {
    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });
    this.context = context;
  }

  protected async createCompletion(
    messages: ChatCompletionMessageParam[], 
    tools?: ChatCompletionTool[], 
    model: string = AI_MODELS.PRIMARY,
    temperature?: number,
    maxTokens?: number,
    responseFormat?: OpenAI.Chat.Completions.ChatCompletionCreateParams.ResponseFormat
  ) {
    const errorContext: ErrorContext = {
      operation: `${this.constructor.name}.createCompletion`,
      projectId: this.context.projectId,
      metadata: {
        model,
        temperature: temperature || AI_TEMPERATURE.BALANCED,
        maxTokens: maxTokens || AI_MAX_TOKENS.STANDARD,
        hasTools: !!tools
      }
    };

    return withRetry(
      async () => {
        return withCircuitBreaker(
          `openai-${this.constructor.name.toLowerCase()}`,
          async () => {
            const completion = await this.openai.chat.completions.create({
              model,
              messages,
              tools,
              tool_choice: tools ? 'auto' : undefined,
              temperature: temperature || AI_TEMPERATURE.BALANCED,
              max_tokens: maxTokens || AI_MAX_TOKENS.STANDARD,
              response_format: responseFormat,
            });

            return completion;
          },
          CIRCUIT_BREAKER_PRESETS.OPENAI
        );
      },
      {
        maxRetries: 3,
        onRetry: (error, attempt, delay) => {
          console.log(`${this.constructor.name}: Retry attempt ${attempt} after ${delay}ms due to:`, error.message);
        }
      },
      errorContext
    );
  }

  protected createSystemPrompt(role: string, instructions: string): string {
    const settingsContext = this.buildSettingsContext();
    
    return `You are ${role}, an expert AI agent specializing in novel writing.

${instructions}

PROJECT SETTINGS CONTEXT:
${settingsContext}

Always maintain consistency with these project settings and previous agent outputs in the context.`;
  }

  /**
   * Create a structured completion with JSON schema validation
   * Falls back to json_object mode for unsupported models
   */
  protected async createStructuredCompletion<T extends z.ZodType>(
    messages: ChatCompletionMessageParam[],
    schema: T,
    schemaName: string,
    model: string = AI_MODELS.PRIMARY,
    temperature?: number,
    maxTokens?: number
  ): Promise<{
    success: boolean;
    data?: z.infer<T>;
    error?: string;
    refusal?: string;
    rawContent?: string;
  }> {
    const errorContext: ErrorContext = {
      operation: `${this.constructor.name}.createStructuredCompletion`,
      projectId: this.context.projectId,
      metadata: {
        model,
        schemaName,
        temperature: temperature || AI_TEMPERATURE.BALANCED,
        maxTokens: maxTokens || AI_MAX_TOKENS.STANDARD,
      }
    };

    try {
      // Import here to avoid circular dependency
      const { createStructuredOutputSchema, parseStructuredOutput } = await import('../utils/structured-outputs');
      
      // Check if model supports structured outputs
      const responseFormat = supportsStructuredOutputs(model)
        ? createStructuredOutputSchema(schema, schemaName)
        : createJsonObjectFallback();

      const response = await withRetry(
        async () => {
          return withCircuitBreaker(
            `openai-${this.constructor.name.toLowerCase()}-structured`,
            async () => {
              const completion = await this.openai.chat.completions.create({
                model,
                messages,
                temperature: temperature || AI_TEMPERATURE.BALANCED,
                max_tokens: maxTokens || AI_MAX_TOKENS.STANDARD,
                response_format: responseFormat,
              });

              return completion;
            },
            CIRCUIT_BREAKER_PRESETS.OPENAI
          );
        },
        {
          maxRetries: 3,
          onRetry: (error, attempt, delay) => {
            console.log(`${this.constructor.name} structured completion: Retry attempt ${attempt} after ${delay}ms due to:`, error.message);
          }
        },
        errorContext
      );

      const message = response.choices[0]?.message;
      if (!message) {
        return {
          success: false,
          error: 'No message received from model'
        };
      }

      // Handle refusal for structured outputs
      const messageWithRefusal = message as OpenAI.Chat.Completions.ChatCompletionMessage & { refusal?: string };
      const refusal = messageWithRefusal.refusal;
      const content = messageWithRefusal.content;

      const result = parseStructuredOutput(content, refusal, schema);
      
      return {
        ...result,
        rawContent: content || undefined
      };
    } catch (error) {
      console.error(`${this.constructor.name} structured completion error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private buildSettingsContext(): string {
    const settings = this.context.settings || this.context.projectSelections;
    
    if (!settings) {
      return 'No project settings available';
    }
    
    return `
GENRE & STYLE:
- Primary Genre: ${settings.primaryGenre}
- Subgenre: ${settings.subgenre || 'None specified'}
- Narrative Voice: ${settings.narrativeVoice}
- Tense: ${settings.tense}
- Writing Style: ${settings.writingStyle}
- Tone: ${settings.tone?.join(', ') || 'None specified'}

STORY STRUCTURE:
- Structure Type: ${settings.structureType}
- Pacing: ${settings.pacingPreference}
- Timeline Complexity: ${settings.timelineComplexity}

WORLD & CHARACTERS:
- Time Period: ${settings.timePeriod}
- World Type: ${settings.worldType}
- Character Complexity: ${settings.characterComplexity}
- Magic/Tech Level: ${settings.magicTechLevel}

THEMES:
- Major Themes: ${settings.majorThemes?.join(', ') || 'None specified'}
- Philosophical Themes: ${settings.philosophicalThemes?.join(', ') || 'None specified'}
- Social Themes: ${settings.socialThemes?.join(', ') || 'None specified'}

TECHNICAL SPECS:
- Target Word Count: ${settings.targetWordCount?.toLocaleString()}
- POV Type: ${settings.povCharacterType}
- Content Rating: ${settings.contentRating}

STORY CONCEPT:
${settings.initialConcept}
    `.trim();
  }

  abstract execute(): Promise<unknown>;
}