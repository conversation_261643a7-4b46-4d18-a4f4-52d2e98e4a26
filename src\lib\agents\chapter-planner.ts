import { BaseAgent } from './base-agent'
import { BookContext } from './types'
import { 
  ChapterPlannerInput, 
  ChapterOutline, 
  AgentResponse 
} from '../types/agents'
import { getAIConfig } from '../config/ai-settings'
import { chapterOutlinesResponseSchema } from '../schemas/agent-schemas'

export class ChapterPlannerAgent extends BaseAgent {
  constructor(context: BookContext) {
    super(context);
  }

  private getConfig() {
    return getAIConfig('CHAPTER_PLANNING');
  }

  async generateChapterOutlines(input: ChapterPlannerInput): Promise<AgentResponse<ChapterOutline[]>> {
    const startTime = Date.now()
    
    try {
      const systemPrompt = this.buildSystemPrompt(input)
      const userPrompt = this.buildUserPrompt(input)
      const config = this.getConfig();

      // Use structured completion from base class
      const result = await this.createStructuredCompletion(
        [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        chapterOutlinesResponseSchema,
        'chapter_outlines',
        config.model,
        config.temperature,
        config.max_tokens
      );

      if (!result.success) {
        // If refusal occurred, include it in the error message
        const errorMessage = result.refusal 
          ? `Model refused to generate content: ${result.refusal}`
          : result.error || 'Failed to generate chapter outlines';
        
        return {
          success: false,
          error: errorMessage,
          executionTime: Date.now() - startTime
        };
      }

      return {
        success: true,
        data: result.data!.chapters,
        executionTime: Date.now() - startTime,
        // Note: token usage would need to be tracked separately
      }
    } catch (error) {
      console.error('Chapter outline generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime: Date.now() - startTime
      }
    }
  }

  private buildSystemPrompt(input: ChapterPlannerInput): string {
    const avgWordsPerChapter = Math.round(input.targetWordCount / input.targetChapters)
    
    return `You are a master Chapter Planner AI agent with the page-turning expertise of James Patterson, the narrative control of Donna Tartt, and the cliffhanger mastery of Dan Brown. Your chapter structures must create the "unputdownable" quality that defines bestsellers.

BESTSELLER CHAPTER ARCHITECTURE:
- Design chapters that readers will discuss in book clubs and online forums
- Create chapter endings that force readers to start "just one more"
- Build rhythm patterns that manipulate reader emotions expertly
- Structure scenes with the precision of a thriller and depth of literary fiction
- Ensure every chapter could work as a compelling short story

PACING MASTERY (${input.projectSelections.pacingPreference}):
- Open each chapter with immediate engagement (no throat-clearing)
- Use the "Scene-Sequel" pattern: Action → Reaction → Decision → Action
- Vary chapter lengths strategically (short for tension, long for immersion)
- Create "breathing room" chapters after intense sequences
- Build to mini-climaxes within each chapter

CHAPTER METRICS:
- Total: ${input.targetChapters} chapters
- Target: ${input.targetWordCount.toLocaleString()} words total
- Average: ${avgWordsPerChapter.toLocaleString()} words per chapter
- But vary lengths for effect (1,500-5,000 word range)

ENGAGEMENT TECHNIQUES:
- Chapter Openings: Start in medias res or with intriguing questions
- Midpoints: Include revelations, reversals, or deepening conflicts
- Endings: Cliffhangers, revelations, or emotional gut-punches
- Transitions: Each chapter should feel inevitable yet surprising

SCENE CONSTRUCTION:
- Goal: What the POV character wants
- Conflict: What prevents them from getting it
- Disaster: How things go wrong (or surprisingly right)
- Emotion: The feeling readers should have after each scene
- Purpose: How this scene changes everything

READER MANIPULATION TECHNIQUES:
- Plant questions that won't be answered for chapters
- Use dramatic irony (reader knows something characters don't)
- Create false victories followed by devastating reversals
- Build sympathy before putting characters in danger
- Layer multiple timeline tensions simultaneously

CHAPTER TITLES:
- Intriguing without spoiling
- Thematically resonant
- Memorable and discussable
- Consider using recurring motifs or countdown structures

Remember: Every chapter is a promise to the reader. Make promises they can't resist, then deliver payoffs that exceed expectations while setting up even bigger promises.`
  }

  private buildUserPrompt(input: ChapterPlannerInput): string {
    const { storyStructure, characters, projectSelections } = input
    
    return `Please create detailed chapter outlines for this story:

STORY STRUCTURE:
Title: ${storyStructure.title}
Genre: ${storyStructure.genre}
Themes: ${storyStructure.themes.join(', ')}

ACTS BREAKDOWN:
${storyStructure.acts.map(act => `
Act ${act.number}: ${act.title} (${act.wordCount.toLocaleString()} words)
${act.description}
Key Events: ${act.keyEvents.join(', ')}
`).join('\n')}

MAIN CONFLICTS:
${storyStructure.conflicts.map(conflict => `${conflict.type}: ${conflict.description}`).join('\n')}

CHARACTER ROSTER:
${characters.map(char => `
${char.name} (${char.role}): ${char.description}
Arc: ${char.arc.startingPoint} → ${char.arc.transformation} → ${char.arc.endingPoint}
`).join('\n')}

PROJECT SPECIFICATIONS:
- Narrative Voice: ${projectSelections.narrativeVoice}
- Timeline Complexity: ${projectSelections.timelineComplexity}
- POV Characters: ${projectSelections.povCharacterCount} (${projectSelections.povCharacterType})
- Chapter Structure: ${projectSelections.chapterStructure}

PACING AND STRUCTURE:
- Overall Pacing: ${projectSelections.pacingPreference}
- Structure Framework: ${projectSelections.structureType}
- Target Chapters: ${input.targetChapters}
- Target Word Count: ${input.targetWordCount.toLocaleString()} words

STORY TIMELINE:
${storyStructure.timeline.map(event => `Chapter ${event.chapter || 'TBD'}: ${event.event} (${event.importance})`).join('\n')}

Create chapter outlines that:
1. Follow the act structure and hit all major story beats
2. Develop characters through their planned arcs
3. Maintain appropriate pacing for the genre and preferences
4. Include compelling hooks and transitions
5. Distribute word count to match the overall target
6. Support the chosen narrative voice and POV structure

Ensure each chapter serves a clear purpose in advancing the story toward its resolution.`
  }
}