/**
 * AI Configuration Settings
 * Centralized configuration for all AI-related settings
 */

export const AI_MODELS = {
  // Primary models for different tasks
  PRIMARY: 'gpt-4.1-2025-04-14',  // Updated to latest GPT-4.1 model that supports structured outputs
  FAST: 'gpt-4o-mini',  // For general fast operations and chat completions
  FAST_REASONING: 'gpt-4o-mini',  // For reasoning-heavy fast operations (o4-mini not yet available)
  EMBEDDING: 'text-embedding-3-small',

  // Alternative models (fallbacks)
  ALTERNATIVE_PRIMARY: 'gpt-4-turbo',
  ALTERNATIVE_FAST: 'gpt-4o-mini',
  LEGACY_PRIMARY: 'gpt-4-0125-preview',
  LEGACY_FAST: 'gpt-3.5-turbo',

  // Model selection based on task
  TASKS: {
    STORY_STRUCTURE: 'gpt-4.1-2025-04-14',  // Using latest GPT-4.1 for structured outputs
    CHARACTER_DEVELOPMENT: 'gpt-4.1-2025-04-14',
    CHAPTER_PLANNING: 'gpt-4.1-2025-04-14',
    CHAPTER_WRITING: 'gpt-4.1-2025-04-14',
    CONTENT_GENERATION: 'gpt-4.1-2025-04-14',
    EDITING: 'gpt-4o-mini',
    ANALYSIS: 'gpt-4o-mini',  // Fast model for analysis
    SUGGESTIONS: 'gpt-4o-mini',  // Fast model for suggestions
    ADAPTIVE_PLANNING: 'gpt-4o-mini'  // Fast model for adaptive planning
  }
} as const;

export const AI_TEMPERATURE = {
  // Temperature settings for different types of generation
  CREATIVE_HIGH: 0.9,      // For highly creative content
  CREATIVE_MEDIUM: 0.8,    // For story generation, character development
  BALANCED: 0.7,           // Default for most tasks
  FOCUSED: 0.5,            // For planning and structure
  PRECISE: 0.3,            // For editing and consistency
  DETERMINISTIC: 0.1,      // For analysis and validation
  
  // Task-specific temperatures
  TASKS: {
    STORY_STRUCTURE: 0.8,
    CHARACTER_DEVELOPMENT: 0.8,
    CHAPTER_PLANNING: 0.7,
    CHAPTER_WRITING: 0.8,
    CONTENT_GENERATION: 0.7,
    EDITING: 0.3,
    ANALYSIS: 0.3,
    SUGGESTIONS: 0.5,
    ADAPTIVE_PLANNING: 0.5
  }
} as const;

export const AI_MAX_TOKENS = {
  // Token limits for different generation types
  EXTRA_LARGE: 10000,   // For complete story structures
  LARGE: 8000,         // For chapter content
  MEDIUM: 6000,        // For character profiles, outlines
  STANDARD: 4000,      // Default for most tasks
  SMALL: 2000,         // For short generations
  TINY: 1000,           // For quick responses
  
  // Task-specific limits
  TASKS: {
    STORY_STRUCTURE: 10000,
    CHARACTER_DEVELOPMENT: 6000,
    CHAPTER_PLANNING: 4000,
    CHAPTER_WRITING: 10000,
    CONTENT_GENERATION: 2000,
    EDITING: 2000,
    ANALYSIS: 1000,
    SUGGESTIONS: 1000,
    ADAPTIVE_PLANNING: 4000
  }
} as const;

export const AI_RETRY_CONFIG = {
  // Retry configuration
  MAX_RETRIES: 3,
  INITIAL_DELAY: 1000,      // 1 second
  BACKOFF_MULTIPLIER: 2,    // Exponential backoff
  MAX_DELAY: 30000,         // 30 seconds max
  TIMEOUT: 120000,          // 2 minutes timeout
  
  // Task-specific retries
  TASKS: {
    CRITICAL: { maxRetries: 5, timeout: 180000 },
    STANDARD: { maxRetries: 3, timeout: 120000 },
    QUICK: { maxRetries: 2, timeout: 60000 }
  }
} as const;

export const AI_CONCURRENCY = {
  // Concurrency limits
  MAX_CONCURRENT_TASKS: 3,
  MAX_CONCURRENT_CHAPTERS: 2,
  MAX_CONCURRENT_ANALYSIS: 5,
  
  // Queue settings
  QUEUE_CHECK_INTERVAL: 1000,  // 1 second
  QUEUE_MAX_SIZE: 100,
  PRIORITY_LEVELS: {
    CRITICAL: 4,
    HIGH: 3,
    MEDIUM: 2,
    LOW: 1
  }
} as const;

export const AI_QUALITY_THRESHOLDS = {
  // Quality assessment thresholds
  BESTSELLER: 95,        // NYT bestseller quality
  EXCELLENT: 90,         // Award-worthy quality
  PUBLISHER_READY: 85,   // Ready for traditional publishing
  GOOD: 80,             // Strong with minor improvements needed
  ACCEPTABLE: 75,       // Baseline acceptable quality
  NEEDS_WORK: 70,       // Requires significant improvement
  POOR: 60,             // Major rewrite needed
  
  // Minimum scores for different content types (raised for bestseller standards)
  MINIMUM_SCORES: {
    CHAPTER_CONTENT: 85,      // Was 75
    CHARACTER_PROFILE: 88,    // Was 80
    STORY_STRUCTURE: 92,      // Was 85
    DIALOGUE: 85,            // Was 70
    DESCRIPTION: 85,         // Was 70
    SCENE: 85,               // New
    PLOT_OUTLINE: 90,        // New
    OPENING_HOOK: 95         // New - First pages must be exceptional
  }
} as const;

export const AI_CONTEXT_LIMITS = {
  // Context window management
  MAX_CONTEXT_TOKENS: 8000,
  RESERVED_OUTPUT_TOKENS: 2000,
  MAX_INPUT_TOKENS: 6000,
  
  // Context compression thresholds
  COMPRESSION_THRESHOLD: 5000,
  SUMMARY_MAX_LENGTH: 1000,
  
  // Memory limits
  MAX_CHAPTER_CONTEXT: 3,     // Number of previous chapters to include
  MAX_CHARACTER_CONTEXT: 10,  // Number of characters to include in context
  MAX_SCENE_CONTEXT: 5        // Number of scenes to include
} as const;

export const AI_RATE_LIMITS = {
  // Rate limiting per minute (based on OpenAI API limits as of 2025)
  REQUESTS_PER_MINUTE: {
    'gpt-4.1-2025-04-14': 500,    // GPT-4.1 tier limits
    'gpt-4o': 500,                // GPT-4o tier limits
    'gpt-4o-mini': 1000,          // GPT-4o mini higher limits
    'gpt-4-turbo': 500,           // GPT-4 Turbo limits
    'gpt-4-0125-preview': 500,    // Legacy GPT-4 limits
    'gpt-3.5-turbo': 3500,        // GPT-3.5 higher limits
    'text-embedding-3-small': 5000, // Embedding limits
    'text-embedding-3-large': 5000  // Embedding limits
  },

  // Token limits per minute (TPM)
  TOKENS_PER_MINUTE: {
    'gpt-4.1-2025-04-14': 300000,    // GPT-4.1 token limits
    'gpt-4o': 300000,                // GPT-4o token limits
    'gpt-4o-mini': 2000000,          // GPT-4o mini higher token limits
    'gpt-4-turbo': 300000,           // GPT-4 Turbo token limits
    'gpt-4-0125-preview': 300000,    // Legacy GPT-4 token limits
    'gpt-3.5-turbo': 2000000,        // GPT-3.5 higher token limits
    'text-embedding-3-small': 5000000, // Embedding token limits
    'text-embedding-3-large': 5000000  // Embedding token limits
  }
} as const;

// Helper functions to get rate limits for specific models
export function getRequestsPerMinute(model: string): number {
  return AI_RATE_LIMITS.REQUESTS_PER_MINUTE[model as keyof typeof AI_RATE_LIMITS.REQUESTS_PER_MINUTE] || 60; // Default fallback
}

export function getTokensPerMinute(model: string): number {
  return AI_RATE_LIMITS.TOKENS_PER_MINUTE[model as keyof typeof AI_RATE_LIMITS.TOKENS_PER_MINUTE] || 40000; // Default fallback
}

// Helper functions for configuration
export function getModelForTask(task: keyof typeof AI_MODELS.TASKS): string {
  return AI_MODELS.TASKS[task] || AI_MODELS.PRIMARY;
}

export function getTemperatureForTask(task: keyof typeof AI_TEMPERATURE.TASKS): number {
  return AI_TEMPERATURE.TASKS[task] || AI_TEMPERATURE.BALANCED;
}

export function getMaxTokensForTask(task: keyof typeof AI_MAX_TOKENS.TASKS): number {
  return AI_MAX_TOKENS.TASKS[task] || AI_MAX_TOKENS.STANDARD;
}

export function getRetryConfigForPriority(priority: 'CRITICAL' | 'STANDARD' | 'QUICK') {
  return AI_RETRY_CONFIG.TASKS[priority] || AI_RETRY_CONFIG.TASKS.STANDARD;
}

// Dynamic configuration based on user preferences
export interface UserAIPreferences {
  preferredModel?: string;
  creativityLevel?: 'high' | 'medium' | 'low';
  outputLength?: 'short' | 'medium' | 'long';
  qualityVsSpeed?: 'quality' | 'balanced' | 'speed';
}

export function getAIConfig(
  task: keyof typeof AI_MODELS.TASKS,
  userPrefs?: UserAIPreferences
) {
  let model = getModelForTask(task);
  let temperature = getTemperatureForTask(task);
  let maxTokens = getMaxTokensForTask(task);
  
  // Apply user preferences
  if (userPrefs) {
    // Model preference
    if (userPrefs.preferredModel) {
      model = userPrefs.preferredModel;
    } else if (userPrefs.qualityVsSpeed === 'speed') {
      model = AI_MODELS.FAST;
    }
    
    // Temperature based on creativity
    if (userPrefs.creativityLevel === 'high') {
      temperature = Math.min(temperature + 0.2, 1.0);
    } else if (userPrefs.creativityLevel === 'low') {
      temperature = Math.max(temperature - 0.2, 0.1);
    }
    
    // Token limit based on length preference
    if (userPrefs.outputLength === 'long') {
      maxTokens = Math.min(maxTokens * 1.5, AI_MAX_TOKENS.EXTRA_LARGE);
    } else if (userPrefs.outputLength === 'short') {
      maxTokens = Math.max(maxTokens * 0.5, AI_MAX_TOKENS.TINY);
    }
  }
  
  return {
    model,
    temperature,
    max_tokens: Math.floor(maxTokens),
    top_p: 1,
    frequency_penalty: 0,
    presence_penalty: 0
  };
}

// Export default configuration
export const DEFAULT_AI_CONFIG = {
  model: AI_MODELS.PRIMARY,
  temperature: AI_TEMPERATURE.BALANCED,
  max_tokens: AI_MAX_TOKENS.STANDARD,
  retryConfig: AI_RETRY_CONFIG.TASKS.STANDARD,
  concurrency: AI_CONCURRENCY.MAX_CONCURRENT_TASKS
} as const;