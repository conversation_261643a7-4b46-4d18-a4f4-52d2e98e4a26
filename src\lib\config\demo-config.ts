// Demo configuration for running BookScribe without external services
// This provides dummy values for all required environment variables

export const demoConfig = {
  // App Configuration
  NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
  NODE_ENV: 'development' as const,
  
  // Supabase Configuration (dummy values for demo)
  NEXT_PUBLIC_SUPABASE_URL: 'https://demo.supabase.co',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: 'demo_anon_key_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
  SUPABASE_SERVICE_ROLE_KEY: 'demo_service_role_key_eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
  SUPABASE_WEBHOOK_SECRET: 'demo_webhook_secret',
  SUPABASE_JWT_SECRET: 'demo_jwt_secret',
  
  // OpenAI Configuration (dummy key for demo)
  OPENAI_API_KEY: 'sk-demo_openai_key_1234567890abcdef',
  
  // Google Gemini Configuration (optional)
  GENKIT_API_KEY: 'demo_genkit_key',
  
  // Stripe Configuration (dummy values for demo)
  STRIPE_SECRET_KEY: 'sk_test_demo_stripe_secret_key_1234567890',
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: 'pk_test_demo_stripe_publishable_key_1234567890',
  STRIPE_WEBHOOK_SECRET: 'whsec_demo_stripe_webhook_secret',
  
  // Stripe Price IDs (dummy values for demo)
  STRIPE_PRICE_ID_BASIC: 'price_demo_basic_1234567890',
  STRIPE_PRICE_ID_PRO: 'price_demo_pro_1234567890',
  STRIPE_PRICE_ID_ENTERPRISE: 'price_demo_enterprise_1234567890',
  
  // Admin Configuration
  ADMIN_EMAILS: '<EMAIL>,<EMAIL>',
  
  // Development Configuration
  NEXT_PUBLIC_DEV_BYPASS_AUTH: 'true',
  DEV_USER_EMAIL: '<EMAIL>',
  DEV_USER_ID: 'demo_user_12345',
  
  // Build Configuration
  npm_package_version: '0.1.0',
  NEXT_PHASE: 'development',
  
  // Demo Mode Flag
  NEXT_PUBLIC_DEMO_MODE: 'true',
}

// Type for demo config to ensure it matches the expected schema
export type DemoConfig = typeof demoConfig

// Helper to check if we're using demo config
export const isDemoConfig = (config: unknown): config is DemoConfig => {
  return (config as DemoConfig)?.NEXT_PUBLIC_DEMO_MODE === 'true'
}

// Demo-specific constants
export const DEMO_CONSTANTS = {
  // Demo user data
  DEMO_USER: {
    id: 'demo_user_12345',
    email: '<EMAIL>',
    name: 'Demo User',
    avatar_url: null,
    subscription_tier: 'pro' as const,
  },
  
  // Demo project data
  DEMO_PROJECT: {
    id: 'demo_project_12345',
    title: 'The Chronicles of Aethermoor',
    description: 'A fantasy epic about a young mage discovering her powers',
    genre: 'Fantasy',
    target_word_count: 80000,
    current_word_count: 47832,
    status: 'in_progress' as const,
  },
  
  // Demo subscription data
  DEMO_SUBSCRIPTION: {
    id: 'demo_sub_12345',
    status: 'active' as const,
    tier: 'pro' as const,
    current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    cancel_at_period_end: false,
  },
  
  // Demo analytics data
  DEMO_ANALYTICS: {
    total_words: 47832,
    words_today: 1847,
    writing_streak: 23,
    avg_words_per_day: 1654,
    total_sessions: 156,
    total_time_minutes: 9360, // 156 hours
  },
} as const
