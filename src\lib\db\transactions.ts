import { createClient } from '@/lib/supabase/server'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from './types'

export type TransactionClient = SupabaseClient<Database>

export interface TransactionResult<T> {
  success: boolean
  data?: T
  error?: Error | string
}

/**
 * Execute a database transaction using Supabase RPC
 * Note: Requires a corresponding PostgreSQL function to be created
 */
export async function executeTransaction<T = unknown>(
  functionName: string,
  params: Record<string, unknown>
): Promise<TransactionResult<T>> {
  try {
    const supabase = await createClient()
    const { data, error } = await supabase.rpc(functionName, params)
    
    if (error) {
      console.error(`Transaction ${functionName} failed:`, error)
      return { success: false, error: error.message }
    }
    
    return { success: true, data: data as T }
  } catch (error) {
    console.error(`Transaction ${functionName} error:`, error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Transaction failed' 
    }
  }
}

/**
 * Helper to create a compensating transaction for rollback scenarios
 */
export class CompensatingTransaction {
  private rollbackActions: Array<() => Promise<void>> = []
  
  addRollback(action: () => Promise<void>) {
    this.rollbackActions.push(action)
  }
  
  async rollback() {
    // Execute rollback actions in reverse order
    for (let i = this.rollbackActions.length - 1; i >= 0; i--) {
      try {
        const action = this.rollbackActions[i]
        if (action) {
          await action()
        }
      } catch (error) {
        console.error('Rollback action failed:', error)
        // Continue with other rollbacks even if one fails
      }
    }
  }
}

/**
 * Batch insert helper for better performance
 */
export async function batchInsert<T extends Record<string, unknown>>(
  tableName: string,
  records: T[],
  chunkSize = 100
): Promise<TransactionResult<T[]>> {
  try {
    const supabase = await createClient()
    const results: T[] = []
    
    // Process in chunks to avoid hitting size limits
    for (let i = 0; i < records.length; i += chunkSize) {
      const chunk = records.slice(i, i + chunkSize)
      const { data, error } = await supabase
        .from(tableName)
        .insert(chunk)
        .select()
      
      if (error) {
        return { success: false, error: error.message }
      }
      
      results.push(...(data || []))
    }
    
    return { success: true, data: results }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Batch insert failed' 
    }
  }
}

/**
 * Batch update helper
 */
export async function batchUpdate<T extends Record<string, unknown>>(
  tableName: string,
  updates: Array<{ id: string; data: Partial<T> }>,
  chunkSize = 50
): Promise<TransactionResult<T[]>> {
  try {
    const supabase = await createClient()
    const results: T[] = []
    
    // Process updates in chunks
    for (let i = 0; i < updates.length; i += chunkSize) {
      const chunk = updates.slice(i, i + chunkSize)
      
      // Execute updates in parallel within chunk
      const promises = chunk.map(({ id, data }) =>
        supabase
          .from(tableName)
          .update(data)
          .eq('id', id)
          .select()
          .single()
      )
      
      const chunkResults = await Promise.all(promises)
      
      for (const result of chunkResults) {
        if (result.error) {
          return { success: false, error: result.error.message }
        }
        if (result.data) {
          results.push(result.data)
        }
      }
    }
    
    return { success: true, data: results }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Batch update failed' 
    }
  }
}

/**
 * Batch delete helper
 */
export async function batchDelete(
  tableName: string,
  ids: string[],
  chunkSize = 100
): Promise<TransactionResult<void>> {
  try {
    const supabase = await createClient()
    
    // Process deletes in chunks
    for (let i = 0; i < ids.length; i += chunkSize) {
      const chunk = ids.slice(i, i + chunkSize)
      const { error } = await supabase
        .from(tableName)
        .delete()
        .in('id', chunk)
      
      if (error) {
        return { success: false, error: error.message }
      }
    }
    
    return { success: true }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Batch delete failed' 
    }
  }
}