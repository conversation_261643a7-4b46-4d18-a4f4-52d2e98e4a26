import { createClient } from '@/lib/supabase/client'
import { CompensatingTransaction, batchInsert } from '../transactions'
import type { Database as _Database } from '../types'

interface SampleProjectData {
  userId: string
  title: string
  description: string
  genre: string
  targetAudience: string
  contentRating: string
  narrativeVoice: string
  tense: string
  targetWordCount: number
  targetChapters: number
  storyArcs: Array<{
    actNumber: number
    description: string
    keyEvents: string[]
  }>
  characters: Array<{
    name: string
    role: string
    description: string
    backstory: string
    personalityTraits: string[]
  }>
  chapters: Array<{
    chapterNumber: number
    title: string
    summary: string
    targetWordCount: number
  }>
  storyBibleEntries: Array<{
    entryType: string
    entryKey: string
    entryData: Record<string, unknown>
  }>
}

export async function createSampleProjectWithTransaction(
  data: SampleProjectData
): Promise<{ success: boolean; projectId?: string; error?: string }> {
  const supabase = createClient()
  const compensation = new CompensatingTransaction()
  let projectId: string | null = null
  
  try {
    // Step 1: Create the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        user_id: data.userId,
        title: data.title,
        description: data.description,
        primary_genre: data.genre,
        target_audience: data.targetAudience,
        content_rating: data.contentRating,
        narrative_voice: data.narrativeVoice,
        tense: data.tense,
        target_word_count: data.targetWordCount,
        target_chapters: data.targetChapters,
        status: 'planning',
        current_word_count: 0
      })
      .select()
      .single()
    
    if (projectError || !project) {
      throw new Error(projectError?.message || 'Failed to create project')
    }
    
    projectId = project.id
    
    // Add rollback to delete project if something fails
    compensation.addRollback(async () => {
      if (projectId) {
        await supabase.from('projects').delete().eq('id', projectId)
      }
    })
    
    // Step 2: Create story arcs
    if (data.storyArcs.length > 0) {
      const arcs = data.storyArcs.map(arc => ({
        project_id: projectId!,
        act_number: arc.actNumber,
        description: arc.description,
        key_events: arc.keyEvents
      }))
      
      const result = await batchInsert('story_arcs', arcs)
      if (!result.success) throw new Error(`Failed to create story arcs: ${result.error}`)
    }
    
    // Step 3: Create characters
    if (data.characters.length > 0) {
      const characters = data.characters.map(char => ({
        project_id: projectId!,
        name: char.name,
        role: char.role,
        description: char.description,
        backstory: char.backstory,
        personality_traits: { traits: char.personalityTraits }
      }))
      
      const result = await batchInsert('characters', characters)
      if (!result.success) throw new Error(`Failed to create characters: ${result.error}`)
    }
    
    // Step 4: Create chapter outlines
    if (data.chapters.length > 0) {
      const chapters = data.chapters.map(ch => ({
        project_id: projectId!,
        chapter_number: ch.chapterNumber,
        title: ch.title,
        target_word_count: ch.targetWordCount,
        outline: JSON.stringify({
          title: ch.title,
          summary: ch.summary,
          number: ch.chapterNumber,
          wordCountTarget: ch.targetWordCount
        }),
        status: 'planned',
        actual_word_count: 0
      }))
      
      const result = await batchInsert('chapters', chapters)
      if (!result.success) throw new Error(`Failed to create chapters: ${result.error}`)
    }
    
    // Step 5: Create story bible entries
    if (data.storyBibleEntries.length > 0) {
      const entries = data.storyBibleEntries.map(entry => ({
        project_id: projectId!,
        entry_type: entry.entryType,
        entry_key: entry.entryKey,
        entry_data: entry.entryData
      }))
      
      const result = await batchInsert('story_bible', entries)
      if (!result.success) throw new Error(`Failed to create story bible: ${result.error}`)
    }
    
    return { success: true, projectId: projectId! }
    
  } catch (error) {
    console.error('Sample project transaction failed:', error)
    
    // Attempt rollback
    await compensation.rollback()
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Transaction failed'
    }
  }
}