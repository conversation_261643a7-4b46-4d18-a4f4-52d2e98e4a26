/**
 * Circuit Breaker implementation for external service calls
 * Prevents cascading failures by temporarily blocking calls to failing services
 */

export interface CircuitBreakerOptions {
  failureThreshold: number; // Number of failures before opening circuit
  successThreshold: number; // Number of successes before closing circuit
  timeout: number; // Time in ms to wait before attempting half-open state
  volumeThreshold: number; // Minimum number of requests before evaluating
  errorFilter?: (error: Error) => boolean; // Which errors count as failures
}

export enum CircuitState {
  CLOSED = 'CLOSED', // Normal operation
  OPEN = 'OPEN', // Blocking requests
  HALF_OPEN = 'HALF_OPEN' // Testing if service recovered
}

export class CircuitBreaker<T = any> {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount = 0;
  private successCount = 0;
  private lastFailureTime?: number;
  private totalRequests = 0;
  private nextAttempt?: number;
  
  constructor(
    private readonly name: string,
    private readonly options: CircuitBreakerOptions = {
      failureThreshold: 5,
      successThreshold: 2,
      timeout: 60000, // 1 minute
      volumeThreshold: 10,
      errorFilter: () => true
    }
  ) {}

  async execute<R>(fn: () => Promise<R>): Promise<R> {
    // Check if circuit is open
    if (this.state === CircuitState.OPEN) {
      if (this.canAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
      } else {
        throw new Error(`Circuit breaker is OPEN for ${this.name}`);
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error as Error);
      throw error;
    }
  }

  private onSuccess(): void {
    this.totalRequests++;
    
    if (this.state === CircuitState.HALF_OPEN) {
      this.successCount++;
      if (this.successCount >= this.options.successThreshold) {
        this.reset();
      }
    } else if (this.state === CircuitState.CLOSED) {
      this.failureCount = 0;
    }
  }

  private onFailure(error: Error): void {
    this.totalRequests++;
    
    // Check if error should be counted
    if (this.options.errorFilter && !this.options.errorFilter(error)) {
      return;
    }

    this.lastFailureTime = Date.now();
    
    if (this.state === CircuitState.HALF_OPEN) {
      this.open();
    } else if (this.state === CircuitState.CLOSED) {
      this.failureCount++;
      
      // Check if we should open the circuit
      if (
        this.totalRequests >= this.options.volumeThreshold &&
        this.failureCount >= this.options.failureThreshold
      ) {
        this.open();
      }
    }
  }

  private open(): void {
    this.state = CircuitState.OPEN;
    this.nextAttempt = Date.now() + this.options.timeout;
    console.warn(`Circuit breaker opened for ${this.name}`);
  }

  private reset(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.nextAttempt = undefined;
    console.info(`Circuit breaker closed for ${this.name}`);
  }

  private canAttemptReset(): boolean {
    return !!this.nextAttempt && Date.now() >= this.nextAttempt;
  }

  getState(): {
    state: CircuitState;
    failureCount: number;
    successCount: number;
    totalRequests: number;
    lastFailureTime?: Date;
    nextAttemptTime?: Date;
  } {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      totalRequests: this.totalRequests,
      lastFailureTime: this.lastFailureTime ? new Date(this.lastFailureTime) : undefined,
      nextAttemptTime: this.nextAttempt ? new Date(this.nextAttempt) : undefined
    };
  }

  // Force reset for manual intervention
  forceReset(): void {
    this.reset();
  }

  // Force open for manual intervention
  forceOpen(): void {
    this.open();
  }
}

// Circuit breaker manager for centralized management
export class CircuitBreakerManager {
  private static instance: CircuitBreakerManager;
  private breakers = new Map<string, CircuitBreaker>();

  private constructor() {}

  static getInstance(): CircuitBreakerManager {
    if (!CircuitBreakerManager.instance) {
      CircuitBreakerManager.instance = new CircuitBreakerManager();
    }
    return CircuitBreakerManager.instance;
  }

  getBreaker(name: string, options?: CircuitBreakerOptions): CircuitBreaker {
    if (!this.breakers.has(name)) {
      this.breakers.set(name, new CircuitBreaker(name, options));
    }
    return this.breakers.get(name)!;
  }

  getAllBreakerStates(): Record<string, ReturnType<CircuitBreaker['getState']>> {
    const states: Record<string, ReturnType<CircuitBreaker['getState']>> = {};
    this.breakers.forEach((breaker, name) => {
      states[name] = breaker.getState();
    });
    return states;
  }

  resetAll(): void {
    this.breakers.forEach(breaker => breaker.forceReset());
  }

  resetBreaker(name: string): void {
    const breaker = this.breakers.get(name);
    if (breaker) {
      breaker.forceReset();
    }
  }
}

// Export singleton instance
export const circuitBreakerManager = CircuitBreakerManager.getInstance();

// Convenience function for using circuit breakers
export async function withCircuitBreaker<T>(
  name: string,
  fn: () => Promise<T>,
  options?: CircuitBreakerOptions
): Promise<T> {
  const breaker = circuitBreakerManager.getBreaker(name, options);
  return breaker.execute(fn);
}

// Default circuit breaker configurations for different services
export const CIRCUIT_BREAKER_PRESETS = {
  OPENAI: {
    failureThreshold: 5,
    successThreshold: 3,
    timeout: 60000, // 1 minute
    volumeThreshold: 10,
    errorFilter: (error: Error) => {
      // Don't count rate limits as failures (they're expected)
      return !error.message.includes('rate limit');
    }
  },
  SUPABASE: {
    failureThreshold: 10,
    successThreshold: 5,
    timeout: 30000, // 30 seconds
    volumeThreshold: 20
  },
  STRIPE: {
    failureThreshold: 3,
    successThreshold: 2,
    timeout: 120000, // 2 minutes
    volumeThreshold: 5
  }
} as const;