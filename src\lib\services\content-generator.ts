import OpenAI from 'openai';
import { BaseService } from './base-service';
import { WritingTask, ServiceResponse } from './types';
import { config } from '@/lib/config';
import { getAIConfig, AI_QUALITY_THRESHOLDS, AI_TEMPERATURE } from '../config/ai-settings';
import { qualityAnalyzer } from './quality-analyzer';
import { withRetry, ErrorContext } from './error-handler';
import { withCircuitBreaker, CIRCUIT_BREAKER_PRESETS } from './circuit-breaker';
import { 
  createStructuredOutputSchema, 
  parseStructuredOutput, 
  supportsStructuredOutputs,
  createJsonObjectFallback 
} from '../utils/structured-outputs';
import {
  sceneOutlineSchema,
  dialogueResponseSchema,
  characterProfileGenerationSchema,
  worldBuildingSchema
} from '../schemas/content-schemas';

interface GenerationResult {
  content?: string;
  metadata?: Record<string, unknown>;
  quality?: number;
}

export class ContentGenerator extends BaseService {
  private openai: OpenAI;
  private generationQueue: WritingTask[] = [];
  private activeGenerations: Map<string, { task: WritingTask; startTime: number }> = new Map();

  constructor() {
    super({
      name: 'content-generator',
      version: '1.0.0',
      status: 'inactive',
      endpoints: ['/api/content/generate', '/api/content/templates'],
      dependencies: ['ai-orchestrator'],
      healthCheck: '/api/content/health'
    });

    this.openai = new OpenAI({
      apiKey: config.openai.apiKey,
    });
  }

  async initialize(): Promise<void> {
    this.startGenerationProcessor();
    this.isInitialized = true;
    this.setStatus('active');
  }

  async healthCheck(): Promise<ServiceResponse<{ status: string; uptime: number }>> {
    return this.createResponse(true, {
      status: `${this.generationQueue.length} queued, ${this.activeGenerations.size} processing`,
      uptime: Date.now() - (this.isInitialized ? Date.now() - 1000 : Date.now()),
    });
  }

  async shutdown(): Promise<void> {
    this.generationQueue = [];
    this.activeGenerations.clear();
    this.setStatus('inactive');
  }

  async generateContent(request: {
    type: 'scene' | 'dialogue' | 'description' | 'chapter' | 'character' | 'plot-outline';
    prompt: string;
    context?: Record<string, unknown>;
    style?: string;
    length?: 'short' | 'medium' | 'long';
    tone?: string;
    projectId: string;
  }): Promise<ServiceResponse<string>> {
    return this.withErrorHandling(async () => {
      const task: WritingTask = {
        id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        projectId: request.projectId,
        type: 'generate',
        priority: 'medium',
        input: {
          prompt: request.prompt,
          context: {
            type: request.type,
            style: request.style,
            length: request.length,
            tone: request.tone,
            ...request.context
          }
        },
        status: 'pending',
        createdAt: Date.now()
      };

      this.generationQueue.push(task);
      
      // Wait for completion
      return new Promise((resolve, reject) => {
        const checkCompletion = () => {
          if (task.status === 'completed' && task.output) {
            resolve(task.output.content);
          } else if (task.status === 'failed') {
            reject(new Error('Generation failed'));
          } else {
            setTimeout(checkCompletion, 500);
          }
        };
        
        setTimeout(checkCompletion, 100);
        
        // Timeout after 5 minutes for complex generation tasks
        setTimeout(() => {
          if (task.status === 'processing' || task.status === 'pending') {
            reject(new Error('Generation timeout'));
          }
        }, 300000);
      });
    });
  }

  async generateSceneOutline(request: {
    sceneGoal: string;
    characters: string[];
    setting: string;
    previousEvents: string;
    projectId: string;
  }): Promise<ServiceResponse<{
    outline: string;
    keyEvents: string[];
    conflicts: string[];
    emotionalBeats: string[];
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create a detailed scene outline with the following parameters:

Scene Goal: ${request.sceneGoal}
Characters Present: ${request.characters.join(', ')}
Setting: ${request.setting}
Previous Events: ${request.previousEvents}

Provide:
1. A structured scene outline (3-5 key beats)
2. List of key events that must happen
3. Potential conflicts or tensions
4. Emotional beats for character development

Format as JSON with keys: outline, keyEvents, conflicts, emotionalBeats
      `;

      const aiConfig = getAIConfig('CONTENT_GENERATION');
      
      const errorContext: ErrorContext = {
        operation: 'ContentGenerator.generateSceneOutline',
        projectId: request.projectId,
        metadata: {
          sceneGoal: request.sceneGoal,
          model: aiConfig.model
        }
      };

      // Use structured outputs if supported, otherwise fall back to json_object
      const responseFormat = supportsStructuredOutputs(aiConfig.model)
        ? createStructuredOutputSchema(sceneOutlineSchema, 'scene_outline', 'Scene outline with key events and emotional beats')
        : createJsonObjectFallback();

      const completion = await withRetry(
        async () => withCircuitBreaker(
          'openai-content-generator',
          () => this.openai.chat.completions.create({
          model: aiConfig.model,
          messages: [
            {
              role: 'system',
              content: 'You are an expert story architect. Create detailed, engaging scene outlines that drive plot and character development forward.'
            },
            { role: 'user', content: prompt }
          ],
          temperature: aiConfig.temperature,
          max_tokens: aiConfig.max_tokens,
          response_format: responseFormat
        }),
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
        {
          maxRetries: 2,
          onRetry: (error, attempt) => {
            console.log(`Scene outline generation retry ${attempt}:`, error.message);
          }
        },
        errorContext
      );

      const message = completion.choices[0]?.message;
      if (!message) {
        throw new Error('No message received from model');
      }

      // Handle structured output response
      if (supportsStructuredOutputs(aiConfig.model)) {
        const messageWithRefusal = message as OpenAI.Chat.Completions.ChatCompletionMessage & { refusal?: string };
        const result = parseStructuredOutput(
          messageWithRefusal.content,
          messageWithRefusal.refusal,
          sceneOutlineSchema
        );
        
        if (!result.success) {
          throw new Error(result.error);
        }
        
        return result.data;
      }
      
      // Fallback for json_object mode
      const content = message.content || '{}';
      try {
        return JSON.parse(content);
      } catch {
        return {
          outline: content,
          keyEvents: [],
          conflicts: [],
          emotionalBeats: []
        };
      }
    });
  }

  async generateDialogue(request: {
    characters: { name: string; personality: string; goal: string }[];
    context: string;
    tone: string;
    length: number;
    projectId: string;
  }): Promise<ServiceResponse<{
    dialogue: { speaker: string; line: string; emotion?: string }[];
    tags: string[];
    subtext: string[];
  }>> {
    return this.withErrorHandling(async () => {
      const characterDescriptions = request.characters
        .map(char => `${char.name}: ${char.personality} (Goal: ${char.goal})`)
        .join('\n');

      const prompt = `
Write a dialogue scene between these characters:
${characterDescriptions}

Context: ${request.context}
Tone: ${request.tone}
Target length: Approximately ${request.length} lines

Requirements:
- Each character should speak in their distinct voice
- Include subtle subtext and character motivations
- Show personality through speech patterns
- Create natural flow and realistic interruptions

Format as JSON with:
{
  "dialogue": [{"speaker": "Name", "line": "dialogue text", "emotion": "optional"}],
  "tags": ["relevant", "story", "tags"],
  "subtext": ["underlying meanings", "character motivations"]
}
      `;

      const aiConfig = getAIConfig('CONTENT_GENERATION');
      
      const errorContext: ErrorContext = {
        operation: 'ContentGenerator.generateDialogue',
        projectId: request.projectId,
        metadata: {
          characterCount: request.characters.length,
          tone: request.tone,
          model: aiConfig.model
        }
      };
      
      // Use structured outputs if supported
      const responseFormat = supportsStructuredOutputs(aiConfig.model)
        ? createStructuredOutputSchema(dialogueResponseSchema, 'dialogue_scene', 'Character dialogue with emotions and subtext')
        : createJsonObjectFallback();

      const completion = await withRetry(
        async () => withCircuitBreaker(
          'openai-content-generator',
          () => this.openai.chat.completions.create({
          model: aiConfig.model,
          messages: [
            {
              role: 'system',
              content: 'You are an expert dialogue writer. Create realistic, character-driven dialogue that advances plot and reveals personality.'
            },
            { role: 'user', content: prompt }
          ],
          temperature: aiConfig.temperature,
          max_tokens: aiConfig.max_tokens,
          response_format: responseFormat
        }),
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
        {
          maxRetries: 2,
          onRetry: (error, attempt) => {
            console.log(`Dialogue generation retry ${attempt}:`, error.message);
          }
        },
        errorContext
      );

      const message = completion.choices[0]?.message;
      if (!message) {
        throw new Error('No message received from model');
      }

      // Handle structured output response
      if (supportsStructuredOutputs(aiConfig.model)) {
        const messageWithRefusal = message as OpenAI.Chat.Completions.ChatCompletionMessage & { refusal?: string };
        const result = parseStructuredOutput(
          messageWithRefusal.content,
          messageWithRefusal.refusal,
          dialogueResponseSchema
        );
        
        if (!result.success) {
          throw new Error(result.error);
        }
        
        return result.data;
      }
      
      // Fallback for json_object mode
      const content = message.content || '{}';
      try {
        return JSON.parse(content);
      } catch {
        return {
          dialogue: [{ speaker: 'Unknown', line: content }],
          tags: [],
          subtext: []
        };
      }
    });
  }

  async generateCharacterProfile(request: {
    name: string;
    role: 'protagonist' | 'antagonist' | 'supporting';
    age?: number;
    background?: string;
    goals?: string[];
    conflicts?: string[];
    projectId: string;
  }): Promise<ServiceResponse<{
    profile: {
      name: string;
      age: number;
      appearance: string;
      personality: string;
      background: string;
      motivations: string[];
      fears: string[];
      strengths: string[];
      weaknesses: string[];
      relationships: { character: string; relationship: string }[];
      arc: string;
      voice: string;
    };
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create a comprehensive character profile for:

Name: ${request.name}
Role: ${request.role}
${request.age ? `Age: ${request.age}` : ''}
${request.background ? `Background: ${request.background}` : ''}
${request.goals ? `Goals: ${request.goals.join(', ')}` : ''}
${request.conflicts ? `Conflicts: ${request.conflicts.join(', ')}` : ''}

Generate a detailed character profile including:
- Physical appearance
- Personality traits and quirks
- Background and history
- Core motivations and goals
- Deepest fears
- Key strengths and weaknesses
- Important relationships
- Character arc potential
- Distinctive voice/speech patterns

Format as JSON matching the profile structure.
      `;

      const aiConfig = getAIConfig('CHARACTER_DEVELOPMENT');
      
      const errorContext: ErrorContext = {
        operation: 'ContentGenerator.generateCharacterProfile',
        projectId: request.projectId,
        metadata: {
          characterName: request.name,
          role: request.role,
          model: aiConfig.model
        }
      };
      
      // Use structured outputs if supported
      const responseFormat = supportsStructuredOutputs(aiConfig.model)
        ? createStructuredOutputSchema(characterProfileGenerationSchema, 'character_profile', 'Comprehensive character profile with background and traits')
        : createJsonObjectFallback();

      const completion = await withRetry(
        async () => withCircuitBreaker(
          'openai-content-generator',
          () => this.openai.chat.completions.create({
          model: aiConfig.model,
          messages: [
            {
              role: 'system',
              content: 'You are an expert character developer. Create rich, complex characters with depth, flaws, and compelling motivations.'
            },
            { role: 'user', content: prompt }
          ],
          temperature: aiConfig.temperature,
          max_tokens: aiConfig.max_tokens,
          response_format: responseFormat
        }),
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
        {
          maxRetries: 2,
          onRetry: (error, attempt) => {
            console.log(`Character profile generation retry ${attempt}:`, error.message);
          }
        },
        errorContext
      );

      const message = completion.choices[0]?.message;
      if (!message) {
        throw new Error('No message received from model');
      }

      // Handle structured output response
      if (supportsStructuredOutputs(aiConfig.model)) {
        const messageWithRefusal = message as OpenAI.Chat.Completions.ChatCompletionMessage & { refusal?: string };
        const result = parseStructuredOutput(
          messageWithRefusal.content,
          messageWithRefusal.refusal,
          characterProfileGenerationSchema
        );
        
        if (!result.success) {
          throw new Error(result.error);
        }
        
        return result.data;
      }
      
      // Fallback for json_object mode
      const content = message.content || '{}';
      try {
        const profile = JSON.parse(content);
        return { profile };
      } catch {
        return {
          profile: {
            name: request.name,
            age: request.age || 25,
            appearance: 'To be determined',
            personality: content.substring(0, 200),
            background: request.background || 'To be determined',
            motivations: request.goals || [],
            fears: [],
            strengths: [],
            weaknesses: [],
            relationships: [],
            arc: 'To be determined',
            voice: 'To be determined'
          }
        };
      }
    });
  }

  async generateWorldBuilding(request: {
    type: 'location' | 'culture' | 'history' | 'magic-system' | 'technology';
    name: string;
    description: string;
    genre: string;
    projectId: string;
  }): Promise<ServiceResponse<{
    worldElement: {
      name: string;
      type: string;
      description: string;
      details: Record<string, unknown>;
      connections: string[];
      atmosphere: string;
      significance: string;
    };
  }>> {
    return this.withErrorHandling(async () => {
      const prompt = `
Create detailed world-building for a ${request.genre} story:

Type: ${request.type}
Name: ${request.name}
Description: ${request.description}

Generate comprehensive details including:
- Physical/conceptual characteristics
- Cultural or functional significance
- Historical context
- Rules or limitations (if applicable)
- Atmospheric elements
- Connections to other story elements
- Story significance and potential

Format as JSON with the worldElement structure.
      `;

      const aiConfig = getAIConfig('CONTENT_GENERATION');
      
      const errorContext: ErrorContext = {
        operation: 'ContentGenerator.generateWorldBuilding',
        projectId: request.projectId,
        metadata: {
          type: request.type,
          name: request.name,
          genre: request.genre,
          model: aiConfig.model
        }
      };
      
      // Use structured outputs if supported
      const responseFormat = supportsStructuredOutputs(aiConfig.model)
        ? createStructuredOutputSchema(worldBuildingSchema, 'world_element', 'Detailed world-building element with atmosphere and connections')
        : createJsonObjectFallback();

      const completion = await withRetry(
        async () => withCircuitBreaker(
          'openai-content-generator',
          () => this.openai.chat.completions.create({
          model: aiConfig.model,
          messages: [
            {
              role: 'system',
              content: 'You are an expert world-builder. Create rich, internally consistent world elements that enhance storytelling.'
            },
            { role: 'user', content: prompt }
          ],
          temperature: aiConfig.temperature,
          max_tokens: aiConfig.max_tokens,
          response_format: responseFormat
        }),
        CIRCUIT_BREAKER_PRESETS.OPENAI
      ),
        {
          maxRetries: 2,
          onRetry: (error, attempt) => {
            console.log(`World building generation retry ${attempt}:`, error.message);
          }
        },
        errorContext
      );

      const message = completion.choices[0]?.message;
      if (!message) {
        throw new Error('No message received from model');
      }

      // Handle structured output response
      if (supportsStructuredOutputs(aiConfig.model)) {
        const messageWithRefusal = message as OpenAI.Chat.Completions.ChatCompletionMessage & { refusal?: string };
        const result = parseStructuredOutput(
          messageWithRefusal.content,
          messageWithRefusal.refusal,
          worldBuildingSchema
        );
        
        if (!result.success) {
          throw new Error(result.error);
        }
        
        return result.data;
      }
      
      // Fallback for json_object mode
      const content = message.content || '{}';
      try {
        const worldElement = JSON.parse(content);
        return { worldElement };
      } catch {
        return {
          worldElement: {
            name: request.name,
            type: request.type,
            description: request.description,
            details: { raw: content },
            connections: [],
            atmosphere: 'To be determined',
            significance: 'To be determined'
          }
        };
      }
    });
  }

  private async processGenerationQueue(): Promise<void> {
    if (this.generationQueue.length === 0 || this.activeGenerations.size >= 3) {
      return; // No tasks or max concurrent generations reached
    }

    const task = this.generationQueue.shift();
    if (!task) return;

    task.status = 'processing';
    this.activeGenerations.set(task.id, { task, startTime: Date.now() });

    try {
      const result = await this.executeGeneration(task);
      const generationResult = result as GenerationResult;
      task.output = {
        content: String(generationResult.content || ''),
        metadata: generationResult.metadata || {},
        quality: Number(generationResult.quality || 0.8)
      };
      task.status = 'completed';
      task.completedAt = Date.now();
    } catch (error) {
      task.status = 'failed';
      console.error(`Generation failed for task ${task.id}:`, error);
    } finally {
      this.activeGenerations.delete(task.id);
    }
  }

  private async executeGeneration(task: WritingTask): Promise<Record<string, unknown>> {
    const context = task.input.context || {};
    const type = context.type || 'general';
    
    const systemPrompts = {
      scene: `You are a master scene writer with the cinematic vision of Cormac McCarthy, the tension-building of Stephen King, and the emotional depth of Elena Ferrante. Create scenes that readers will remember years later.

SCENE EXCELLENCE:
- Start in medias res—no warming up
- Every scene must change something fundamental
- Layer action with internal reaction
- Use specific, unexpected details that reveal character
- End with readers desperate to know what happens next`,
      
      dialogue: `You are a dialogue master who studied under Elmore Leonard, Aaron Sorkin, and Sally Rooney. Your conversations crackle with authenticity, subtext, and revelation.

DIALOGUE MASTERY:
- Each character's voice must be instantly recognizable
- Subtext is everything—what's not said matters most
- Use interruptions, silence, and action beats for rhythm
- Never use dialogue for exposition dumps
- Make every line earn its place through conflict or character revelation`,
      
      description: `You are a descriptive virtuoso with Annie Proulx's eye for detail, Gabriel García Márquez's magical touch, and Donna Tartt's atmospheric mastery. Paint worlds readers can taste, smell, and touch.

DESCRIPTIVE EXCELLENCE:
- Engage all five senses in unexpected ways
- Use specific details that do double duty (setting + emotion/character)
- Create atmosphere that enhances mood without slowing pace
- Make settings almost characters themselves
- Show the familiar through fresh eyes`,
      
      chapter: `You are a chapter architect with James Patterson's pacing, George R.R. Martin's complexity, and Tana French's atmospheric control. Structure chapters that readers can't put down.

CHAPTER CRAFTSMANSHIP:
- Hook readers in the first line
- Build multiple layers of tension throughout
- Balance scene and sequel for emotional impact
- Create cliffhangers that feel organic, not manipulative
- Ensure every chapter could work as a compelling short story`,
      
      character: `You are a character creator with the psychological insight of Gillian Flynn, the complexity of George Eliot, and the relatability of Rainbow Rowell. Birth characters who feel more real than real people.

CHARACTER CREATION:
- Give each character a secret that changes everything
- Create contradictions that feel authentically human
- Build in specific details that readers will remember
- Design flaws that make characters more loveable
- Ensure each character could carry their own novel`,
      
      'plot-outline': `You are a plot architect with the structural genius of Christopher Nolan, the twists of Agatha Christie, and the emotional journeys of Khaled Hosseini. Design stories that haunt readers.

PLOT EXCELLENCE:
- Every plot point must feel surprising yet inevitable
- Layer multiple timeline tensions
- Create reversals that recontextualize everything
- Build to emotional climaxes, not just plot climaxes
- Ensure the ending satisfies while leaving readers wanting more`,
      
      general: 'You are a bestselling author with deep expertise across all aspects of commercial and literary fiction. Adapt your approach to create excellence in any writing task.'
    };

    const systemPrompt = systemPrompts[type as keyof typeof systemPrompts] || systemPrompts.general;

    let userPrompt = task.input.prompt || '';
    
    if (context.style) userPrompt += `\n\nStyle: ${context.style}`;
    if (context.tone) userPrompt += `\nTone: ${context.tone}`;
    if (context.length) userPrompt += `\nLength: ${context.length}`;

    const aiConfig = getAIConfig('CONTENT_GENERATION');
    const temperature = type === 'dialogue' ? AI_TEMPERATURE.CREATIVE_MEDIUM : aiConfig.temperature;
    const maxTokens = context.length === 'long' ? 3000 : context.length === 'medium' ? 1500 : 1000;
    
    const errorContext: ErrorContext = {
      operation: 'ContentGenerator.executeGeneration',
      metadata: {
        taskId: task.id,
        type: type,
        model: aiConfig.model
      }
    };
    
    const completion = await withRetry(
      async () => withCircuitBreaker(
        'openai-content-generator',
        () => this.openai.chat.completions.create({
        model: aiConfig.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: temperature,
        max_tokens: Math.min(maxTokens, aiConfig.max_tokens),
      }),
      CIRCUIT_BREAKER_PRESETS.OPENAI
    ),
      {
        maxRetries: 2,
        onRetry: (error, attempt) => {
          console.log(`Content generation retry ${attempt} for task ${task.id}:`, error.message);
        }
      },
      errorContext
    );

    const content = completion.choices[0]?.message?.content || '';
    
    const quality = await this.assessGenerationQuality(content, String(type));
    
    return {
      content,
      metadata: {
        type: type,
        model: 'gpt-4',
        processingTime: Date.now() - (this.activeGenerations.get(task.id)?.startTime || Date.now()),
        tokensUsed: completion.usage?.total_tokens || 0,
        style: String(context.style || ''),
        tone: String(context.tone || '')
      },
      quality
    };
  }

  private async assessGenerationQuality(content: string, type: string): Promise<number> {
    // Try to use the quality analyzer if it's available
    if (qualityAnalyzer.getStatus() === 'active') {
      try {
        const analysisResult = await qualityAnalyzer.analyzeContentQuality(
          content,
          type as 'chapter' | 'dialogue' | 'description' | 'scene' | 'character'
        );
        
        if (analysisResult.success && analysisResult.data) {
          return analysisResult.data.metrics.overall;
        }
      } catch (error) {
        console.warn('Quality analyzer failed, falling back to basic assessment:', error);
      }
    }

    // Fallback to basic quality assessment
    let score = AI_QUALITY_THRESHOLDS.NEEDS_IMPROVEMENT; // Base score

    // Length appropriateness
    if (content.length > 200) score += 10;
    if (content.length > 1000) score += 5;

    // Structure check
    const paragraphs = content.split('\n\n').filter(p => p.trim());
    if (paragraphs.length > 1) score += 10;

    // Type-specific quality checks
    switch (type) {
      case 'dialogue':
        if (content.includes('"') && content.includes('said')) score += 10;
        break;
      case 'description':
        if (content.match(/\b(saw|heard|felt|smelled|tasted)\b/gi)) score += 10;
        break;
      case 'scene':
        if (content.includes('"') && paragraphs.length >= 3) score += 15;
        break;
    }

    // Readability check
    const sentences = content.split(/[.!?]+/).filter(s => s.trim());
    const avgSentenceLength = content.split(/\s+/).length / sentences.length;
    if (avgSentenceLength > 8 && avgSentenceLength < 30) score += 5;

    // Ensure score meets minimum threshold for content type
    const minimumScore = AI_QUALITY_THRESHOLDS.MINIMUM_SCORES[
      type.toUpperCase() as keyof typeof AI_QUALITY_THRESHOLDS.MINIMUM_SCORES
    ] || AI_QUALITY_THRESHOLDS.ACCEPTABLE;
    
    return Math.min(100, Math.max(minimumScore * 0.5, score));
  }

  private startGenerationProcessor(): void {
    setInterval(() => {
      this.processGenerationQueue();
    }, 1000);
  }
}