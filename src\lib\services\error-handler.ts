import { AI_RETRY_CONFIG } from '../config/ai-settings';

export interface RetryOptions {
  maxRetries?: number;
  initialDelay?: number;
  maxDelay?: number;
  backoffMultiplier?: number;
  timeout?: number;
  retryCondition?: (error: Error) => boolean;
  onRetry?: (error: Error, attempt: number, delay: number) => void;
}

export interface ErrorContext {
  operation: string;
  projectId?: string;
  userId?: string;
  metadata?: Record<string, unknown>;
}

export class AIError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public context?: ErrorContext,
    public isRetryable: boolean = true
  ) {
    super(message);
    this.name = 'AIError';
  }
}

export class RateLimitError extends AIError {
  constructor(
    message: string,
    public resetTime: Date,
    context?: ErrorContext
  ) {
    super(message, 'RATE_LIMIT_ERROR', 429, context, true);
    this.name = 'RateLimitError';
  }
}

export class ValidationError extends AIError {
  constructor(
    message: string,
    public validationErrors: Record<string, string[]>,
    context?: ErrorContext
  ) {
    super(message, 'VALIDATION_ERROR', 400, context, false);
    this.name = 'ValidationError';
  }
}

export class ConsistencyError extends AIError {
  constructor(
    message: string,
    public inconsistencies: Array<{ type: string; description: string }>,
    context?: ErrorContext
  ) {
    super(message, 'CONSISTENCY_ERROR', 422, context, false);
    this.name = 'ConsistencyError';
  }
}

export class TokenLimitError extends AIError {
  constructor(
    message: string,
    public tokensUsed: number,
    public tokenLimit: number,
    context?: ErrorContext
  ) {
    super(message, 'TOKEN_LIMIT_ERROR', 400, context, false);
    this.name = 'TokenLimitError';
  }
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLog: Array<{ timestamp: Date; error: Error; context?: ErrorContext }> = [];
  private maxLogSize = 1000;

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Execute a function with retry logic and exponential backoff
   */
  async withRetry<T>(
    fn: () => Promise<T>,
    options: RetryOptions = {},
    context?: ErrorContext
  ): Promise<T> {
    const {
      maxRetries = AI_RETRY_CONFIG.MAX_RETRIES,
      initialDelay = AI_RETRY_CONFIG.INITIAL_DELAY,
      maxDelay = AI_RETRY_CONFIG.MAX_DELAY,
      backoffMultiplier = AI_RETRY_CONFIG.BACKOFF_MULTIPLIER,
      timeout = AI_RETRY_CONFIG.TIMEOUT,
      retryCondition = this.defaultRetryCondition,
      onRetry
    } = options;

    let lastError: Error | undefined;
    let attempt = 0;

    const executeWithTimeout = async (): Promise<T> => {
      return Promise.race([
        fn(),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Operation timed out')), timeout)
        )
      ]);
    };

    while (attempt <= maxRetries) {
      try {
        return await executeWithTimeout();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        this.logError(lastError, context);

        // Check if we should retry
        if (attempt >= maxRetries || !retryCondition(lastError)) {
          throw this.enhanceError(lastError, context);
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          initialDelay * Math.pow(backoffMultiplier, attempt),
          maxDelay
        );

        // Add jitter to prevent thundering herd
        const jitteredDelay = delay * (0.5 + Math.random() * 0.5);

        if (onRetry) {
          onRetry(lastError, attempt + 1, jitteredDelay);
        }

        await this.delay(jitteredDelay);
        attempt++;
      }
    }

    throw this.enhanceError(
      lastError || new Error('Max retries exceeded'),
      context
    );
  }

  /**
   * Handle errors and provide appropriate responses
   */
  handleError(error: unknown, context?: ErrorContext): AIError {
    if (error instanceof AIError) {
      return error;
    }

    if (error instanceof Error) {
      // Handle specific error types
      if (error.message.includes('rate limit')) {
        return new RateLimitError(
          'API rate limit exceeded',
          new Date(Date.now() + 60000), // 1 minute default
          context
        );
      }

      if (error.message.includes('token') && error.message.includes('limit')) {
        return new TokenLimitError(
          'Token limit exceeded',
          0, // Would need to parse from error
          8000, // Default limit
          context
        );
      }

      if (error.name === 'ValidationError') {
        return new ValidationError(
          error.message,
          {}, // Would need to parse validation errors
          context
        );
      }

      // Generic AI error
      return new AIError(
        error.message,
        'UNKNOWN_ERROR',
        500,
        context,
        this.isRetryableError(error)
      );
    }

    // Handle non-Error objects
    return new AIError(
      String(error),
      'UNKNOWN_ERROR',
      500,
      context,
      false
    );
  }

  /**
   * Create error recovery strategies
   */
  async recoverFromError<T>(
    error: Error,
    fallbackFn: () => Promise<T>,
    context?: ErrorContext
  ): Promise<T> {
    const aiError = this.handleError(error, context);

    // Log the error for analysis
    this.logError(error, context);

    // Implement recovery strategies based on error type
    switch (aiError.code) {
      case 'RATE_LIMIT_ERROR':
        // Wait for rate limit reset
        const rateLimitError = aiError as RateLimitError;
        const waitTime = rateLimitError.resetTime.getTime() - Date.now();
        if (waitTime > 0 && waitTime < 60000) { // Max 1 minute wait
          await this.delay(waitTime);
          return fallbackFn();
        }
        break;

      case 'TOKEN_LIMIT_ERROR':
        // Could implement content truncation or summarization
        console.warn('Token limit exceeded, using fallback');
        break;

      case 'CONSISTENCY_ERROR':
        // Could implement consistency resolution
        console.warn('Consistency error detected, using fallback');
        break;
    }

    // Use fallback function
    return fallbackFn();
  }

  /**
   * Batch error handling for multiple operations
   */
  async handleBatchOperations<T>(
    operations: Array<() => Promise<T>>,
    options: {
      continueOnError?: boolean;
      maxConcurrent?: number;
      retryOptions?: RetryOptions;
    } = {}
  ): Promise<{ successful: T[]; failed: Array<{ index: number; error: AIError }> }> {
    const {
      continueOnError = true,
      maxConcurrent = 3,
      retryOptions = {}
    } = options;

    const results: T[] = [];
    const failures: Array<{ index: number; error: AIError }> = [];

    // Process in chunks
    for (let i = 0; i < operations.length; i += maxConcurrent) {
      const chunk = operations.slice(i, i + maxConcurrent);
      const chunkPromises = chunk.map(async (operation, chunkIndex) => {
        const operationIndex = i + chunkIndex;
        try {
          const result = await this.withRetry(operation, retryOptions);
          results[operationIndex] = result;
        } catch (error) {
          const aiError = this.handleError(error);
          failures.push({ index: operationIndex, error: aiError });
          
          if (!continueOnError) {
            throw aiError;
          }
        }
      });

      await Promise.all(chunkPromises);
    }

    return {
      successful: results.filter((r): r is T => r !== undefined),
      failed: failures
    };
  }

  /**
   * Default retry condition
   */
  private defaultRetryCondition(error: Error): boolean {
    // Don't retry validation errors
    if (error.name === 'ValidationError') return false;
    
    // Don't retry if explicitly marked as non-retryable
    if (error instanceof AIError && !error.isRetryable) return false;

    // Retry on network errors
    if (error.message.includes('ECONNRESET') || 
        error.message.includes('ETIMEDOUT') ||
        error.message.includes('network')) {
      return true;
    }

    // Retry on rate limits
    if (error.message.includes('rate limit')) return true;

    // Retry on 5xx errors
    if (error instanceof AIError && error.statusCode >= 500) return true;

    // Default to retry
    return true;
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: Error): boolean {
    return this.defaultRetryCondition(error);
  }

  /**
   * Enhance error with additional context
   */
  private enhanceError(error: Error, context?: ErrorContext): AIError {
    if (error instanceof AIError) {
      if (context && !error.context) {
        error.context = context;
      }
      return error;
    }

    return this.handleError(error, context);
  }

  /**
   * Log error for analysis
   */
  private logError(error: Error, context?: ErrorContext): void {
    this.errorLog.push({
      timestamp: new Date(),
      error,
      context
    });

    // Maintain log size
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift();
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', {
        error: error.message,
        context,
        stack: error.stack
      });
    }
  }

  /**
   * Delay helper
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<string, number>;
    recentErrors: Array<{ timestamp: Date; message: string; code?: string }>;
  } {
    const byType: Record<string, number> = {};
    
    this.errorLog.forEach(entry => {
      const type = entry.error.name || 'Unknown';
      byType[type] = (byType[type] || 0) + 1;
    });

    return {
      total: this.errorLog.length,
      byType,
      recentErrors: this.errorLog.slice(-10).map(entry => ({
        timestamp: entry.timestamp,
        message: entry.error.message,
        code: entry.error instanceof AIError ? entry.error.code : undefined
      }))
    };
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Export convenience functions
export async function withRetry<T>(
  fn: () => Promise<T>,
  options?: RetryOptions,
  context?: ErrorContext
): Promise<T> {
  return errorHandler.withRetry(fn, options, context);
}

export function handleError(error: unknown, context?: ErrorContext): AIError {
  return errorHandler.handleError(error, context);
}

export async function recoverFromError<T>(
  error: Error,
  fallbackFn: () => Promise<T>,
  context?: ErrorContext
): Promise<T> {
  return errorHandler.recoverFromError(error, fallbackFn, context);
}