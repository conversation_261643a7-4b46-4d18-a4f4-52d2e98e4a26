/**
 * Streaming Response Handler
 * Handles streaming responses from OpenAI API for better UX
 */

import OpenAI from 'openai';
import { EventEmitter } from 'events';

export interface StreamingOptions {
  onToken?: (token: string) => void;
  onProgress?: (progress: StreamProgress) => void;
  onComplete?: (content: string) => void;
  onError?: (error: Error) => void;
}

export interface StreamProgress {
  tokens: number;
  estimatedTokens: number;
  percentComplete: number;
  content: string;
}

export class StreamingHandler extends EventEmitter {
  private content: string = '';
  private tokens: number = 0;
  private estimatedTokens: number;

  constructor(estimatedTokens: number = 2000) {
    super();
    this.estimatedTokens = estimatedTokens;
  }

  async handleStream(
    stream: AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>,
    options?: StreamingOptions
  ): Promise<string> {
    try {
      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta?.content || '';
        
        if (delta) {
          this.content += delta;
          this.tokens++;

          // Emit token event
          if (options?.onToken) {
            options.onToken(delta);
          }
          this.emit('token', delta);

          // Emit progress event
          const progress: StreamProgress = {
            tokens: this.tokens,
            estimatedTokens: this.estimatedTokens,
            percentComplete: Math.min((this.tokens / this.estimatedTokens) * 100, 95),
            content: this.content
          };
          
          if (options?.onProgress) {
            options.onProgress(progress);
          }
          this.emit('progress', progress);
        }

        // Check for stop
        if (chunk.choices[0]?.finish_reason) {
          break;
        }
      }

      // Final progress
      const finalProgress: StreamProgress = {
        tokens: this.tokens,
        estimatedTokens: this.tokens,
        percentComplete: 100,
        content: this.content
      };

      if (options?.onProgress) {
        options.onProgress(finalProgress);
      }
      this.emit('progress', finalProgress);

      if (options?.onComplete) {
        options.onComplete(this.content);
      }
      this.emit('complete', this.content);

      return this.content;
    } catch (error) {
      if (options?.onError) {
        options.onError(error as Error);
      }
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Create a streaming completion with progress tracking
   */
  static async streamCompletion(
    openai: OpenAI,
    params: OpenAI.Chat.Completions.ChatCompletionCreateParams,
    options?: StreamingOptions
  ): Promise<string> {
    const handler = new StreamingHandler(params.max_tokens || 2000);
    
    const stream = await openai.chat.completions.create({
      ...params,
      stream: true
    });

    return handler.handleStream(stream, options);
  }

  /**
   * Parse streaming JSON responses
   */
  static async streamJSON<T>(
    openai: OpenAI,
    params: OpenAI.Chat.Completions.ChatCompletionCreateParams,
    options?: StreamingOptions
  ): Promise<T> {
    let jsonBuffer = '';
    let inJSON = false;
    let braceCount = 0;

    const content = await StreamingHandler.streamCompletion(openai, params, {
      ...options,
      onToken: (token) => {
        // Track JSON structure
        if (token.includes('{')) {
          inJSON = true;
          braceCount += (token.match(/{/g) || []).length;
        }
        if (token.includes('}')) {
          braceCount -= (token.match(/}/g) || []).length;
        }

        if (inJSON) {
          jsonBuffer += token;
        }

        // Call original onToken if provided
        if (options?.onToken) {
          options.onToken(token);
        }

        // Try to parse when we have complete JSON
        if (inJSON && braceCount === 0 && jsonBuffer.trim()) {
          try {
            const parsed = JSON.parse(jsonBuffer);
            // Emit parsed event
            if (parsed) {
              // We'll return this at the end
            }
          } catch {
            // Not valid JSON yet, continue
          }
        }
      }
    });

    // Try to extract and parse JSON from the content
    try {
      // First try to parse the entire content
      return JSON.parse(content) as T;
    } catch {
      // Try to find JSON in the content
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]) as T;
      }
      throw new Error('No valid JSON found in response');
    }
  }
}

/**
 * Create a progress tracker for long operations
 */
export class ProgressTracker {
  private stages: Map<string, { weight: number; progress: number }> = new Map();
  private currentStage: string = '';

  constructor(stages: Array<{ name: string; weight: number }>) {
    stages.forEach(stage => {
      this.stages.set(stage.name, { weight: stage.weight, progress: 0 });
    });
  }

  setStage(stage: string): void {
    if (this.stages.has(stage)) {
      this.currentStage = stage;
    }
  }

  updateProgress(progress: number): void {
    if (this.currentStage && this.stages.has(this.currentStage)) {
      const stage = this.stages.get(this.currentStage)!;
      stage.progress = Math.min(Math.max(progress, 0), 100);
    }
  }

  getOverallProgress(): number {
    let totalWeight = 0;
    let weightedProgress = 0;

    this.stages.forEach(stage => {
      totalWeight += stage.weight;
      weightedProgress += stage.weight * stage.progress;
    });

    return totalWeight > 0 ? weightedProgress / totalWeight : 0;
  }

  getStageProgress(): Array<{ name: string; progress: number }> {
    return Array.from(this.stages.entries()).map(([name, stage]) => ({
      name,
      progress: stage.progress
    }));
  }
}