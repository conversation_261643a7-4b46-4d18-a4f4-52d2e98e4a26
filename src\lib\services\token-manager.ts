/**
 * Token Management Service
 * Handles token counting, cost estimation, and context window management
 */

import { encoding_for_model, TiktokenModel } from 'tiktoken';
import { AI_MODELS, AI_CONTEXT_LIMITS } from '../config/ai-settings';

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  estimatedCost: number;
}

export interface ModelPricing {
  input: number;  // Cost per 1K tokens
  output: number; // Cost per 1K tokens
}

// Pricing per 1K tokens (in USD)
export const MODEL_PRICING: Record<string, ModelPricing> = {
  'gpt-4.1-2025-04-14': { input: 0.005, output: 0.015 },
  'gpt-4o': { input: 0.005, output: 0.015 },
  'gpt-4o-mini': { input: 0.00015, output: 0.0006 },
  'gpt-4-turbo': { input: 0.01, output: 0.03 },
  'gpt-4-0125-preview': { input: 0.01, output: 0.03 },
  'gpt-3.5-turbo': { input: 0.0005, output: 0.0015 },
  'text-embedding-3-small': { input: 0.00002, output: 0 }
};

export class TokenManager {
  private static instance: TokenManager;
  private encoders: Map<string, any> = new Map();
  private usageHistory: TokenUsage[] = [];

  private constructor() {}

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Count tokens for a given text and model
   */
  countTokens(text: string, model: string = AI_MODELS.PRIMARY): number {
    try {
      // Map our model names to tiktoken model names
      let tiktokenModel: TiktokenModel = 'gpt-4';
      
      if (model.includes('gpt-4') || model.includes('gpt-4.1')) {
        tiktokenModel = 'gpt-4';
      } else if (model.includes('gpt-3.5')) {
        tiktokenModel = 'gpt-3.5-turbo';
      }

      // Get or create encoder for model
      if (!this.encoders.has(tiktokenModel)) {
        this.encoders.set(tiktokenModel, encoding_for_model(tiktokenModel));
      }

      const encoder = this.encoders.get(tiktokenModel);
      const tokens = encoder.encode(text);
      return tokens.length;
    } catch (error) {
      // Fallback: rough estimation (1 token ≈ 4 characters)
      console.warn('Token counting failed, using estimation:', error);
      return Math.ceil(text.length / 4);
    }
  }

  /**
   * Count tokens for messages (chat format)
   */
  countMessageTokens(messages: Array<{ role: string; content: string }>, model: string = AI_MODELS.PRIMARY): number {
    let totalTokens = 0;
    
    // Each message has overhead tokens
    const messageOverhead = 4; // <im_start>{role}\n{content}<im_end>\n
    
    for (const message of messages) {
      totalTokens += this.countTokens(message.content, model) + messageOverhead;
      totalTokens += this.countTokens(message.role, model);
    }
    
    // Add base prompt tokens
    totalTokens += 2; // <im_start>assistant
    
    return totalTokens;
  }

  /**
   * Check if content fits within context window
   */
  fitsInContext(
    content: string | Array<{ role: string; content: string }>,
    model: string = AI_MODELS.PRIMARY,
    reserveTokens: number = AI_CONTEXT_LIMITS.RESERVED_OUTPUT_TOKENS
  ): boolean {
    const tokenCount = Array.isArray(content)
      ? this.countMessageTokens(content, model)
      : this.countTokens(content, model);
      
    return tokenCount + reserveTokens <= AI_CONTEXT_LIMITS.MAX_CONTEXT_TOKENS;
  }

  /**
   * Truncate content to fit within context window
   */
  truncateToFit(
    content: string,
    model: string = AI_MODELS.PRIMARY,
    maxTokens: number = AI_CONTEXT_LIMITS.MAX_INPUT_TOKENS,
    preserveEnd: boolean = false
  ): string {
    const currentTokens = this.countTokens(content, model);
    
    if (currentTokens <= maxTokens) {
      return content;
    }

    // Binary search for the right length
    let low = 0;
    let high = content.length;
    let bestFit = '';

    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      const truncated = preserveEnd 
        ? content.slice(content.length - mid)
        : content.slice(0, mid);
      
      const tokens = this.countTokens(truncated, model);
      
      if (tokens <= maxTokens) {
        bestFit = truncated;
        if (preserveEnd) {
          high = mid - 1;
        } else {
          low = mid + 1;
        }
      } else {
        if (preserveEnd) {
          low = mid + 1;
        } else {
          high = mid - 1;
        }
      }
    }

    // Add ellipsis to indicate truncation
    if (preserveEnd && bestFit.length < content.length) {
      bestFit = '...' + bestFit;
    } else if (!preserveEnd && bestFit.length < content.length) {
      bestFit = bestFit + '...';
    }

    return bestFit;
  }

  /**
   * Estimate cost for token usage
   */
  estimateCost(
    promptTokens: number,
    completionTokens: number,
    model: string = AI_MODELS.PRIMARY
  ): number {
    const pricing = MODEL_PRICING[model] || MODEL_PRICING['gpt-4o'];
    
    const inputCost = (promptTokens / 1000) * pricing.input;
    const outputCost = (completionTokens / 1000) * pricing.output;
    
    return inputCost + outputCost;
  }

  /**
   * Track token usage
   */
  trackUsage(usage: TokenUsage): void {
    this.usageHistory.push(usage);
    
    // Keep only last 1000 entries
    if (this.usageHistory.length > 1000) {
      this.usageHistory.shift();
    }
  }

  /**
   * Get usage statistics
   */
  getUsageStats(since?: Date): {
    totalTokens: number;
    totalCost: number;
    averageTokensPerRequest: number;
    requestCount: number;
  } {
    const relevantUsage = since
      ? this.usageHistory.filter(u => new Date(u.estimatedCost) >= since)
      : this.usageHistory;

    const totalTokens = relevantUsage.reduce((sum, u) => sum + u.totalTokens, 0);
    const totalCost = relevantUsage.reduce((sum, u) => sum + u.estimatedCost, 0);
    const requestCount = relevantUsage.length;
    const averageTokensPerRequest = requestCount > 0 ? totalTokens / requestCount : 0;

    return {
      totalTokens,
      totalCost,
      averageTokensPerRequest,
      requestCount
    };
  }

  /**
   * Smart context management for long conversations
   */
  manageConversationContext(
    messages: Array<{ role: string; content: string }>,
    model: string = AI_MODELS.PRIMARY,
    maxTokens: number = AI_CONTEXT_LIMITS.MAX_INPUT_TOKENS
  ): Array<{ role: string; content: string }> {
    // Always keep the first system message and last user message
    if (messages.length <= 2) return messages;

    const systemMessage = messages[0];
    const lastUserMessage = messages[messages.length - 1];
    let middleMessages = messages.slice(1, -1);

    // Check if all messages fit
    const totalTokens = this.countMessageTokens(messages, model);
    if (totalTokens <= maxTokens) return messages;

    // Start trimming from the middle, keeping recent context
    const result = [systemMessage];
    let currentTokens = this.countMessageTokens([systemMessage, lastUserMessage], model);

    // Add messages from the end, working backwards
    for (let i = middleMessages.length - 1; i >= 0; i--) {
      const messageTokens = this.countMessageTokens([middleMessages[i]], model);
      if (currentTokens + messageTokens <= maxTokens) {
        result.splice(1, 0, middleMessages[i]);
        currentTokens += messageTokens;
      } else {
        // Add a summary message to indicate truncation
        result.splice(1, 0, {
          role: 'system',
          content: `[Previous ${i + 1} messages truncated due to context limit]`
        });
        break;
      }
    }

    result.push(lastUserMessage);
    return result;
  }

  /**
   * Cleanup encoders to free memory
   */
  cleanup(): void {
    this.encoders.forEach(encoder => {
      if (encoder.free) {
        encoder.free();
      }
    });
    this.encoders.clear();
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance();

// Export convenience functions
export function countTokens(text: string, model?: string): number {
  return tokenManager.countTokens(text, model);
}

export function fitsInContext(content: string | Array<{ role: string; content: string }>, model?: string): boolean {
  return tokenManager.fitsInContext(content, model);
}

export function truncateToFit(content: string, model?: string, maxTokens?: number): string {
  return tokenManager.truncateToFit(content, model, maxTokens);
}

export function estimateCost(promptTokens: number, completionTokens: number, model?: string): number {
  return tokenManager.estimateCost(promptTokens, completionTokens, model);
}