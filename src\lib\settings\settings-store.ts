/**
 * Settings Store for BookScribe AI
 * Manages user settings persistence and application
 */

'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  UserSettings, 
  defaultUserSettings, 
  TypographySettings, 
  ThemeSettings,
  EditorSettings,
  AccessibilitySettings,
  textSizeMap,
  fontFamilyMap,
  lineHeightMap,
  letterSpacingMap
} from './settings-types';

interface SettingsStore {
  // Settings state
  settings: UserSettings;
  
  // Actions
  updateTypography: (typography: Partial<TypographySettings>) => void;
  updateTheme: (theme: Partial<ThemeSettings>) => void;
  updateEditor: (editor: Partial<EditorSettings>) => void;
  updateAccessibility: (accessibility: Partial<AccessibilitySettings>) => void;
  resetSettings: () => void;
  
  // Computed values
  getComputedStyles: () => Record<string, string>;
  applySettingsToDocument: () => void;
}

export const useSettingsStore = create<SettingsStore>()(
  persist(
    (set, get) => ({
      settings: defaultUserSettings,

      updateTypography: (typography) => {
        set((state) => ({
          settings: {
            ...state.settings,
            typography: {
              ...state.settings.typography,
              ...typography,
            },
            lastUpdated: new Date().toISOString(),
          },
        }));
        
        // Apply changes immediately
        requestAnimationFrame(() => get().applySettingsToDocument());
      },

      updateTheme: (theme) => {
        set((state) => ({
          settings: {
            ...state.settings,
            theme: {
              ...state.settings.theme,
              ...theme,
            },
            lastUpdated: new Date().toISOString(),
          },
        }));
      },

      updateEditor: (editor) => {
        set((state) => ({
          settings: {
            ...state.settings,
            editor: {
              ...state.settings.editor,
              ...editor,
            },
            lastUpdated: new Date().toISOString(),
          },
        }));
        
        // Apply changes immediately
        requestAnimationFrame(() => get().applySettingsToDocument());
      },

      updateAccessibility: (accessibility) => {
        set((state) => ({
          settings: {
            ...state.settings,
            accessibility: {
              ...state.settings.accessibility,
              ...accessibility,
            },
            lastUpdated: new Date().toISOString(),
          },
        }));
        
        // Apply changes immediately
        requestAnimationFrame(() => get().applySettingsToDocument());
      },

      resetSettings: () => {
        set({ settings: defaultUserSettings });
        setTimeout(() => get().applySettingsToDocument(), 0);
      },

      getComputedStyles: () => {
        const { typography, accessibility } = get().settings;
        const styles: Record<string, string> = {};

        // Text size
        const sizeConfig = typography.textSize !== 'custom' ? textSizeMap[typography.textSize] : null;
        if (sizeConfig) {
          styles['--settings-editor-font-size'] = sizeConfig.editor;
          styles['--settings-ui-font-size'] = sizeConfig.ui;
          styles['--settings-reading-font-size'] = sizeConfig.reading;
          styles['--settings-text-scale'] = sizeConfig.scale.toString();
        }

        // Custom text size
        if (typography.textSize === 'custom' && typography.customTextSize) {
          const customSize = typography.customTextSize;
          styles['--settings-editor-font-size'] = `${customSize}px`;
          styles['--settings-ui-font-size'] = `${customSize}px`;
          styles['--settings-reading-font-size'] = `${customSize + 2}px`;
          styles['--settings-text-scale'] = (customSize / 14).toString();
        }

        // Font families
        styles['--settings-editor-font'] = fontFamilyMap[typography.editorFont];
        styles['--settings-ui-font'] = fontFamilyMap[typography.uiFont];
        styles['--settings-reading-font'] = fontFamilyMap[typography.readingFont];

        // Line height
        styles['--settings-line-height'] = lineHeightMap[typography.lineHeight].toString();

        // Letter spacing
        styles['--settings-letter-spacing'] = letterSpacingMap[typography.letterSpacing];

        // Accessibility
        if (accessibility.reducedMotion) {
          styles['--settings-animation-duration'] = '0s';
          styles['--settings-transition-duration'] = '0s';
        } else {
          styles['--settings-animation-duration'] = '0.3s';
          styles['--settings-transition-duration'] = '0.2s';
        }

        if (accessibility.highContrast) {
          styles['--settings-contrast-multiplier'] = '1.5';
        } else {
          styles['--settings-contrast-multiplier'] = '1';
        }

        return styles;
      },

      applySettingsToDocument: () => {
        if (typeof document === 'undefined') return;

        const styles = get().getComputedStyles();
        const root = document.documentElement;

        // Apply CSS custom properties
        Object.entries(styles).forEach(([property, value]) => {
          root.style.setProperty(property, value);
        });

        // Apply accessibility classes
        const { accessibility } = get().settings;
        
        root.classList.toggle('reduced-motion', accessibility.reducedMotion);
        root.classList.toggle('high-contrast', accessibility.highContrast);
        root.classList.toggle('enhanced-focus', accessibility.enhancedFocus);
        root.classList.toggle('screen-reader-optimized', accessibility.screenReaderOptimized);

        // Debug: Log applied styles in development
        if (process.env.NODE_ENV === 'development') {
          console.log('BookScribe Settings Applied:', styles);
        }
      },
    }),
    {
      name: 'bookscribe-settings',
      version: 1,
      migrate: (persistedState: unknown, version: number) => {
        // Handle settings migration if needed
        if (version === 0) {
          const typedState = persistedState as Partial<UserSettings>;
          return {
            ...defaultUserSettings,
            ...typedState,
          };
        }
        return persistedState as Partial<UserSettings>;
      },
    }
  )
);

// Hook to get current typography settings
export const useTypographySettings = () => {
  const typography = useSettingsStore((state) => state.settings.typography);
  const updateTypography = useSettingsStore((state) => state.updateTypography);
  return { typography, updateTypography };
};

// Hook to get current theme settings
export const useThemeSettings = () => {
  const theme = useSettingsStore((state) => state.settings.theme);
  const updateTheme = useSettingsStore((state) => state.updateTheme);
  return { theme, updateTheme };
};

// Hook to get current editor settings
export const useEditorSettings = () => {
  const editor = useSettingsStore((state) => state.settings.editor);
  const updateEditor = useSettingsStore((state) => state.updateEditor);
  return { editor, updateEditor };
};

// Hook to get current accessibility settings
export const useAccessibilitySettings = () => {
  const accessibility = useSettingsStore((state) => state.settings.accessibility);
  const updateAccessibility = useSettingsStore((state) => state.updateAccessibility);
  return { accessibility, updateAccessibility };
};

// Hook to apply settings on mount
export const useApplySettings = () => {
  const applySettingsToDocument = useSettingsStore((state) => state.applySettingsToDocument);
  
  React.useEffect(() => {
    applySettingsToDocument();
  }, [applySettingsToDocument]);
};

// Import React for useEffect
import React from 'react';
