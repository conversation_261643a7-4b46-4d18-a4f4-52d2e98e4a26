import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  // Access environment variables directly to avoid config initialization issues
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    // Check if we're in demo mode (no env vars set)
    if (typeof window !== 'undefined' && (!supabaseUrl && !supabaseAnonKey)) {
      console.warn('🚧 Demo Mode: Using mock Supabase client')
      // Return a mock client for demo mode
      return createBrowserClient(
        'https://demo.supabase.co',
        'demo_anon_key'
      )
    }
    throw new Error('Missing Supabase environment variables')
  }

  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}