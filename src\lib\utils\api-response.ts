/**
 * API Response Standardization
 * Provides consistent response format across all API endpoints
 */

import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { AIError } from '../services/error-handler';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  meta?: ApiMeta;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  field?: string;
}

export interface ApiMeta {
  timestamp: string;
  version: string;
  requestId?: string;
  duration?: number;
}

export class ApiResponseBuilder {
  private static readonly API_VERSION = '1.0.0';

  /**
   * Create a successful response
   */
  static success<T>(
    data: T,
    meta?: Partial<ApiMeta>
  ): NextResponse<ApiResponse<T>> {
    return NextResponse.json({
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        version: this.API_VERSION,
        ...meta
      }
    });
  }

  /**
   * Create an error response
   */
  static error(
    error: Error | ZodError | AIError | string,
    statusCode: number = 500,
    meta?: Partial<ApiMeta>
  ): NextResponse<ApiResponse> {
    let apiError: ApiError;

    if (error instanceof ZodError) {
      // Handle Zod validation errors
      const issues = error.issues.map(issue => ({
        field: issue.path.join('.'),
        message: issue.message
      }));

      apiError = {
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        details: issues
      };
      statusCode = 400;
    } else if (error instanceof AIError) {
      // Handle AI errors
      apiError = {
        code: error.code,
        message: error.message,
        details: error.context
      };
      statusCode = error.statusCode;
    } else if (error instanceof Error) {
      // Handle generic errors
      apiError = {
        code: 'INTERNAL_ERROR',
        message: error.message,
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      };
    } else {
      // Handle string errors
      apiError = {
        code: 'ERROR',
        message: String(error)
      };
    }

    return NextResponse.json(
      {
        success: false,
        error: apiError,
        meta: {
          timestamp: new Date().toISOString(),
          version: this.API_VERSION,
          ...meta
        }
      },
      { status: statusCode }
    );
  }

  /**
   * Create a paginated response
   */
  static paginated<T>(
    items: T[],
    pagination: {
      page: number;
      limit: number;
      total: number;
    },
    meta?: Partial<ApiMeta>
  ): NextResponse<ApiResponse<{ items: T[]; pagination: typeof pagination }>> {
    return NextResponse.json({
      success: true,
      data: {
        items,
        pagination: {
          ...pagination,
          totalPages: Math.ceil(pagination.total / pagination.limit),
          hasNext: pagination.page * pagination.limit < pagination.total,
          hasPrev: pagination.page > 1
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: this.API_VERSION,
        ...meta
      }
    });
  }

  /**
   * Create a no content response
   */
  static noContent(): NextResponse {
    return new NextResponse(null, { status: 204 });
  }

  /**
   * Create a redirect response
   */
  static redirect(url: string, permanent: boolean = false): NextResponse {
    return NextResponse.redirect(url, permanent ? 301 : 302);
  }
}

/**
 * Async handler wrapper for consistent error handling
 */
export function apiHandler<T = any>(
  handler: (req: Request) => Promise<NextResponse<ApiResponse<T>>>
): (req: Request) => Promise<NextResponse<ApiResponse<T>>> {
  return async (req: Request) => {
    const startTime = Date.now();
    const requestId = crypto.randomUUID();

    try {
      const response = await handler(req);
      
      // Add request metadata if not already present
      if (response.headers.get('content-type')?.includes('application/json')) {
        const body = await response.json();
        if (body.meta) {
          body.meta.requestId = requestId;
          body.meta.duration = Date.now() - startTime;
        }
        return NextResponse.json(body, { status: response.status });
      }
      
      return response;
    } catch (error) {
      console.error(`API Error [${requestId}]:`, error);
      return ApiResponseBuilder.error(error as Error, 500, {
        requestId,
        duration: Date.now() - startTime
      });
    }
  };
}

/**
 * Validate request body with Zod schema
 */
export async function validateBody<T>(
  request: Request,
  schema: any
): Promise<T> {
  try {
    const body = await request.json();
    return schema.parse(body) as T;
  } catch (error) {
    if (error instanceof SyntaxError) {
      throw new Error('Invalid JSON in request body');
    }
    throw error;
  }
}

/**
 * Extract and validate query parameters
 */
export function validateQuery<T>(
  searchParams: URLSearchParams,
  schema: any
): T {
  const params: any = {};
  searchParams.forEach((value, key) => {
    // Handle array parameters
    if (params[key]) {
      if (Array.isArray(params[key])) {
        params[key].push(value);
      } else {
        params[key] = [params[key], value];
      }
    } else {
      params[key] = value;
    }
  });

  return schema.parse(params) as T;
}

/**
 * Common API error codes
 */
export const API_ERROR_CODES = {
  // Client errors
  BAD_REQUEST: 'BAD_REQUEST',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMIT: 'RATE_LIMIT',
  
  // Server errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // Business logic errors
  INSUFFICIENT_CREDITS: 'INSUFFICIENT_CREDITS',
  RESOURCE_LOCKED: 'RESOURCE_LOCKED',
  DUPLICATE_RESOURCE: 'DUPLICATE_RESOURCE',
  INVALID_STATE: 'INVALID_STATE'
} as const;