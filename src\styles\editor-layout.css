/* Editor Layout Animations and Polish */

/* Panel Transitions */
.panel-transition {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth panel resize */
.resizable-panel {
  transition: box-shadow 0.2s ease;
}

.resizable-panel.resizing {
  box-shadow: -4px 0 12px -2px rgba(0, 0, 0, 0.1);
}

/* Chapter Navigator Animations */
.chapter-item {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.chapter-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: var(--literary-amber);
  transform: scaleY(0);
  transition: transform 0.2s ease;
}

.chapter-item:hover::before {
  transform: scaleY(1);
}

.chapter-item.active::before {
  transform: scaleY(1);
  background: var(--primary);
}

/* Tab animations */
.tab-content-enter {
  opacity: 0;
  transform: translateY(10px);
}

.tab-content-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.2s ease;
}

/* Collapse/Expand animations */
.panel-collapse {
  animation: panelCollapse 0.3s ease forwards;
}

.panel-expand {
  animation: panelExpand 0.3s ease forwards;
}

@keyframes panelCollapse {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

@keyframes panelExpand {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Toolbar animations */
.toolbar-button {
  transition: all 0.15s ease;
  position: relative;
}

.toolbar-button:hover {
  transform: translateY(-1px);
}

.toolbar-button:active {
  transform: translateY(0);
}

/* Smooth scrollbars */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 48, 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(139, 92, 48, 0.3);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(139, 92, 48, 0.5);
}

/* Focus mode animations */
.focus-mode-overlay {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Panel resize handle improvements */
.resize-handle {
  position: relative;
  cursor: col-resize;
  transition: background-color 0.2s ease;
}

.resize-handle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 32px;
  background-color: currentColor;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 1px;
}

.resize-handle:hover::after {
  opacity: 0.3;
}

.resize-handle.active::after {
  opacity: 0.5;
}

/* Smooth tab transitions */
.tabs-list {
  position: relative;
}

.tabs-list::after {
  content: '';
  position: absolute;
  bottom: 0;
  height: 2px;
  background: var(--primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Button hover effects */
.hover-lift {
  transition: all 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

/* Smooth panel shadows */
.panel-shadow {
  box-shadow: 
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.panel-shadow:hover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(139, 92, 48, 0.1) 0%,
    rgba(139, 92, 48, 0.2) 50%,
    rgba(139, 92, 48, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Smooth content transitions */
.content-fade {
  animation: contentFade 0.4s ease;
}

@keyframes contentFade {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}