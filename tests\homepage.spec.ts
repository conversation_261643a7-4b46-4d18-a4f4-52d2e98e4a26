import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test('should load the homepage', async ({ page }) => {
    await page.goto('/');
    
    // Check that the page loads successfully
    await expect(page).toHaveURL('/');
    
    // Check for common elements that should be present
    await expect(page.locator('html')).toBeVisible();
    
    // Wait for any initial loading to complete
    await page.waitForLoadState('networkidle');
    
    // Check that we don't have any obvious error messages
    const errorMessages = page.locator('text=Error');
    await expect(errorMessages).toHaveCount(0);
  });

  test('should have a title', async ({ page }) => {
    await page.goto('/');
    
    // Check that the page has a title
    await expect(page).toHaveTitle(/.+/);
  });

  test('should not have console errors', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Filter out known non-critical errors
    const criticalErrors = errors.filter(error => 
      !error.includes('favicon.ico') && 
      !error.includes('Chrome DevTools') &&
      !error.includes('Non-Error promise rejection')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });
});